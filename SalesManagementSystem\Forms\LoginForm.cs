using SalesManagementSystem.Data;
using SalesManagementSystem.Services;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace SalesManagementSystem.Forms
{
    public partial class LoginForm : Form
    {
        private readonly AuthenticationService _authService;
        private TextBox txtNomUtilisateur;
        private TextBox txtMotDePasse;
        private Button btnConnecter;
        private Button btnQuitter;
        private Label lblTitre;
        private Label lblNomUtilisateur;
        private Label lblMotDePasse;
        private PictureBox picLogo;
        private Panel panelLogin;
        private Label lblVersion;
        private CheckBox chkAfficherMotDePasse;

        public LoginForm()
        {
            var context = new DatabaseContext();
            var userRepository = new UtilisateurRepository(context);
            _authService = new AuthenticationService(userRepository);
            
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(240, 244, 248);
            this.ClientSize = new Size(500, 400);
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "Connexion - Système de Gestion";
            this.KeyPreview = true;
            
            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            // Panel principal
            panelLogin = new Panel
            {
                Size = new Size(400, 350),
                Location = new Point(50, 25),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            
            // Ajouter une ombre au panel
            panelLogin.Paint += (s, e) =>
            {
                var rect = panelLogin.ClientRectangle;
                rect.Width -= 1;
                rect.Height -= 1;
                e.Graphics.DrawRectangle(new Pen(Color.FromArgb(200, 200, 200)), rect);
            };

            // Logo
            picLogo = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(160, 20),
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.Transparent
            };
            
            // Titre
            lblTitre = new Label
            {
                Text = "SYSTÈME DE GESTION",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Size = new Size(350, 30),
                Location = new Point(25, 110),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Sous-titre
            var lblSousTitre = new Label
            {
                Text = "Ventes et Stocks",
                Font = new Font("Segoe UI", 10, FontStyle.Regular),
                ForeColor = Color.FromArgb(127, 140, 141),
                Size = new Size(350, 20),
                Location = new Point(25, 140),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Label nom d'utilisateur
            lblNomUtilisateur = new Label
            {
                Text = "Nom d'utilisateur:",
                Font = new Font("Segoe UI", 10, FontStyle.Regular),
                ForeColor = Color.FromArgb(52, 73, 94),
                Size = new Size(150, 20),
                Location = new Point(50, 180)
            };

            // TextBox nom d'utilisateur
            txtNomUtilisateur = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(300, 30),
                Location = new Point(50, 200),
                BorderStyle = BorderStyle.FixedSingle,
                Text = "admin" // Valeur par défaut pour les tests
            };

            // Label mot de passe
            lblMotDePasse = new Label
            {
                Text = "Mot de passe:",
                Font = new Font("Segoe UI", 10, FontStyle.Regular),
                ForeColor = Color.FromArgb(52, 73, 94),
                Size = new Size(150, 20),
                Location = new Point(50, 240)
            };

            // TextBox mot de passe
            txtMotDePasse = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(300, 30),
                Location = new Point(50, 260),
                BorderStyle = BorderStyle.FixedSingle,
                UseSystemPasswordChar = true,
                Text = "admin123" // Valeur par défaut pour les tests
            };

            // CheckBox afficher mot de passe
            chkAfficherMotDePasse = new CheckBox
            {
                Text = "Afficher le mot de passe",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(127, 140, 141),
                Size = new Size(200, 20),
                Location = new Point(50, 295),

            };

            // Bouton Se connecter
            btnConnecter = new Button
            {
                Text = "Se connecter",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                Size = new Size(140, 35),
                Location = new Point(50, 320),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnConnecter.FlatAppearance.BorderSize = 0;
            btnConnecter.Click += BtnConnecter_Click;

            // Bouton Quitter
            btnQuitter = new Button
            {
                Text = "Quitter",
                Font = new Font("Segoe UI", 11),
                Size = new Size(140, 35),
                Location = new Point(210, 320),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnQuitter.FlatAppearance.BorderSize = 0;
            btnQuitter.Click += BtnQuitter_Click;

            // Label version
            lblVersion = new Label
            {
                Text = "Version 1.0.0",
                Font = new Font("Segoe UI", 8),
                ForeColor = Color.FromArgb(149, 165, 166),
                Size = new Size(100, 15),
                Location = new Point(10, 375),
                TextAlign = ContentAlignment.BottomLeft
            };

            // Ajouter les contrôles au panel
            panelLogin.Controls.AddRange(new Control[]
            {
                picLogo, lblTitre, lblSousTitre, lblNomUtilisateur, txtNomUtilisateur,
                lblMotDePasse, txtMotDePasse, chkAfficherMotDePasse, btnConnecter, btnQuitter
            });

            // Ajouter les contrôles au form
            this.Controls.AddRange(new Control[] { panelLogin, lblVersion });

            // Événements
            chkAfficherMotDePasse.CheckedChanged += ChkAfficherMotDePasse_CheckedChanged;
            this.KeyDown += LoginForm_KeyDown;
            this.Load += LoginForm_Load;
        }

        private void ChkAfficherMotDePasse_CheckedChanged(object? sender, EventArgs e)
        {
            txtMotDePasse.UseSystemPasswordChar = !chkAfficherMotDePasse.Checked;
        }

        private async void BtnConnecter_Click(object? sender, EventArgs e)
        {
            await ConnecterAsync();
        }

        private void BtnQuitter_Click(object? sender, EventArgs e)
        {
            Application.Exit();
        }

        private void LoginForm_KeyDown(object? sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                _ = ConnecterAsync();
            }
            else if (e.KeyCode == Keys.Escape)
            {
                Application.Exit();
            }
        }

        private async void LoginForm_Load(object? sender, EventArgs e)
        {
            txtNomUtilisateur.Focus();
            
            // Initialiser la base de données
            try
            {
                var context = new DatabaseContext();
                await context.InitializeDatabaseAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'initialisation de la base de données: {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task ConnecterAsync()
        {
            if (string.IsNullOrWhiteSpace(txtNomUtilisateur.Text))
            {
                MessageBox.Show("Veuillez saisir votre nom d'utilisateur.", "Attention",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNomUtilisateur.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtMotDePasse.Text))
            {
                MessageBox.Show("Veuillez saisir votre mot de passe.", "Attention",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtMotDePasse.Focus();
                return;
            }

            btnConnecter.Enabled = false;
            btnConnecter.Text = "Connexion...";

            try
            {
                var result = await _authService.LoginAsync(txtNomUtilisateur.Text.Trim(), txtMotDePasse.Text);

                if (result.IsSuccess)
                {
                    this.Hide();
                    var mainForm = new MainForm();
                    mainForm.FormClosed += (s, e) => this.Close();
                    mainForm.Show();
                }
                else
                {
                    MessageBox.Show(result.ErrorMessage, "Erreur de connexion",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtMotDePasse.Clear();
                    txtNomUtilisateur.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la connexion: {ex.Message}", "Erreur",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnConnecter.Enabled = true;
                btnConnecter.Text = "Se connecter";
            }
        }
    }
}

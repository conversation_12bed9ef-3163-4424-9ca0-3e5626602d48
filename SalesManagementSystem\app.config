<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- Configuration de l'application -->
    <add key="AppName" value="Système de Gestion des Ventes et Stocks" />
    <add key="AppVersion" value="1.0.0" />
    <add key="Company" value="EE Solutions" />
    
    <!-- Configuration de la base de données -->
    <add key="DatabasePath" value="Data\SalesManagement.db" />
    <add key="BackupPath" value="Backups\" />
    <add key="AutoBackup" value="true" />
    <add key="BackupInterval" value="24" />
    
    <!-- Configuration de sécurité -->
    <add key="PasswordComplexity" value="true" />
    <add key="PasswordExpiry" value="90" />
    <add key="MaxLoginAttempts" value="3" />
    <add key="LockoutDuration" value="30" />
    
    <!-- Configuration régionale -->
    <add key="Culture" value="fr-FR" />
    <add key="Currency" value="€" />
    <add key="DateFormat" value="dd/MM/yyyy" />
    <add key="NumberFormat" value="N2" />
    
    <!-- Configuration des rapports -->
    <add key="ReportsPath" value="Reports\" />
    <add key="ExportsPath" value="Exports\" />
    <add key="DefaultPDFViewer" value="" />
    
    <!-- Configuration de l'interface -->
    <add key="Theme" value="Light" />
    <add key="FontFamily" value="Segoe UI" />
    <add key="FontSize" value="10" />
    <add key="EnableTouch" value="true" />
    
    <!-- Configuration des logs -->
    <add key="LogLevel" value="Information" />
    <add key="LogPath" value="Logs\" />
    <add key="LogRetentionDays" value="30" />
    
    <!-- Configuration des notifications -->
    <add key="ShowStockAlerts" value="true" />
    <add key="ShowPaymentReminders" value="true" />
    <add key="ShowExpiryAlerts" value="true" />
    
    <!-- Configuration des impressions -->
    <add key="DefaultPrinter" value="" />
    <add key="PrintPreview" value="true" />
    <add key="PrintCopies" value="1" />
    
    <!-- Configuration des exports -->
    <add key="ExcelTemplate" value="Templates\Invoice.xlsx" />
    <add key="PDFTemplate" value="Templates\Invoice.pdf" />
    <add key="AutoOpenExports" value="true" />
  </appSettings>
  
  <connectionStrings>
    <add name="DefaultConnection" 
         connectionString="Data Source=Data\SalesManagement.db;Version=3;" 
         providerName="Microsoft.Data.Sqlite" />
  </connectionStrings>
  
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <add name="fileListener" 
             type="System.Diagnostics.TextWriterTraceListener" 
             initializeData="Logs\trace.log" />
      </listeners>
    </trace>
  </system.diagnostics>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" 
                          publicKeyToken="b03f5f7f11d50a3a" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>

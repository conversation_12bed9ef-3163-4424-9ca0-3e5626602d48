using System;
using System.Globalization;
using System.Text.RegularExpressions;
using System.IO;
using System.Linq;

namespace SalesManagementSystem.Utils
{
    public static class ValidationHelper
    {
        // Validation des chaînes de caractères
        public static bool IsValidString(string? value, int minLength = 1, int maxLength = int.MaxValue)
        {
            if (string.IsNullOrWhiteSpace(value))
                return minLength == 0;

            return value.Trim().Length >= minLength && value.Trim().Length <= maxLength;
        }

        public static bool IsValidEmail(string? email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
                return emailRegex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }

        public static bool IsValidPhone(string? phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
                return false;

            // Accepte les formats français: 01.23.45.67.89, 01 23 45 67 89, 0123456789, +33123456789
            var phoneRegex = new Regex(@"^(?:\+33|0)[1-9](?:[.\s-]?\d{2}){4}$");
            return phoneRegex.IsMatch(phone.Replace(" ", "").Replace(".", "").Replace("-", ""));
        }

        public static bool IsValidPostalCode(string? postalCode, string country = "FR")
        {
            if (string.IsNullOrWhiteSpace(postalCode))
                return false;

            return country.ToUpper() switch
            {
                "FR" => Regex.IsMatch(postalCode, @"^\d{5}$"),
                "BE" => Regex.IsMatch(postalCode, @"^\d{4}$"),
                "CH" => Regex.IsMatch(postalCode, @"^\d{4}$"),
                "CA" => Regex.IsMatch(postalCode, @"^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$"),
                "US" => Regex.IsMatch(postalCode, @"^\d{5}(-\d{4})?$"),
                _ => postalCode.Length >= 3 && postalCode.Length <= 10
            };
        }

        public static bool IsValidSIRET(string? siret)
        {
            if (string.IsNullOrWhiteSpace(siret))
                return false;

            siret = siret.Replace(" ", "").Replace(".", "");
            
            if (siret.Length != 14 || !siret.All(char.IsDigit))
                return false;

            // Algorithme de Luhn pour SIRET
            int sum = 0;
            for (int i = 0; i < 14; i++)
            {
                int digit = int.Parse(siret[i].ToString());
                if (i % 2 == 1)
                {
                    digit *= 2;
                    if (digit > 9)
                        digit = digit / 10 + digit % 10;
                }
                sum += digit;
            }

            return sum % 10 == 0;
        }

        public static bool IsValidTVANumber(string? tvaNumber, string country = "FR")
        {
            if (string.IsNullOrWhiteSpace(tvaNumber))
                return false;

            tvaNumber = tvaNumber.Replace(" ", "").Replace(".", "").ToUpper();

            return country.ToUpper() switch
            {
                "FR" => Regex.IsMatch(tvaNumber, @"^FR[A-Z0-9]{2}\d{9}$"),
                "BE" => Regex.IsMatch(tvaNumber, @"^BE0\d{9}$"),
                "DE" => Regex.IsMatch(tvaNumber, @"^DE\d{9}$"),
                "IT" => Regex.IsMatch(tvaNumber, @"^IT\d{11}$"),
                "ES" => Regex.IsMatch(tvaNumber, @"^ES[A-Z0-9]\d{7}[A-Z0-9]$"),
                _ => tvaNumber.Length >= 8 && tvaNumber.Length <= 15
            };
        }

        // Validation des nombres
        public static bool IsValidDecimal(string? value, decimal min = decimal.MinValue, decimal max = decimal.MaxValue)
        {
            if (string.IsNullOrWhiteSpace(value))
                return false;

            if (!decimal.TryParse(value, NumberStyles.Number, CultureInfo.CurrentCulture, out decimal result))
                return false;

            return result >= min && result <= max;
        }

        public static bool IsValidInteger(string? value, int min = int.MinValue, int max = int.MaxValue)
        {
            if (string.IsNullOrWhiteSpace(value))
                return false;

            if (!int.TryParse(value, out int result))
                return false;

            return result >= min && result <= max;
        }

        public static bool IsValidPercentage(string? value)
        {
            return IsValidDecimal(value, 0, 100);
        }

        public static bool IsValidPrice(string? value)
        {
            return IsValidDecimal(value, 0, decimal.MaxValue);
        }

        public static bool IsValidQuantity(string? value)
        {
            return IsValidDecimal(value, 0, decimal.MaxValue);
        }

        // Validation des dates
        public static bool IsValidDate(string? value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return false;

            return DateTime.TryParse(value, CultureInfo.CurrentCulture, DateTimeStyles.None, out _);
        }

        public static bool IsValidDateRange(DateTime? startDate, DateTime? endDate)
        {
            if (!startDate.HasValue || !endDate.HasValue)
                return false;

            return startDate.Value <= endDate.Value;
        }

        public static bool IsValidFutureDate(DateTime? date)
        {
            if (!date.HasValue)
                return false;

            return date.Value.Date >= DateTime.Today;
        }

        public static bool IsValidPastDate(DateTime? date)
        {
            if (!date.HasValue)
                return false;

            return date.Value.Date <= DateTime.Today;
        }

        // Validation des codes
        public static bool IsValidProductCode(string? code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            // Code produit: lettres, chiffres, tirets et underscores, 3-20 caractères
            return Regex.IsMatch(code, @"^[A-Za-z0-9_-]{3,20}$");
        }

        public static bool IsValidClientCode(string? code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            // Code client: lettres, chiffres, tirets et underscores, 2-15 caractères
            return Regex.IsMatch(code, @"^[A-Za-z0-9_-]{2,15}$");
        }

        public static bool IsValidBarcode(string? barcode)
        {
            if (string.IsNullOrWhiteSpace(barcode))
                return false;

            // Code-barres: chiffres uniquement, 8, 12 ou 13 caractères (EAN8, UPC, EAN13)
            return Regex.IsMatch(barcode, @"^\d{8}$|^\d{12}$|^\d{13}$");
        }

        // Validation des mots de passe
        public static bool IsValidPassword(string? password, bool requireComplexity = true)
        {
            if (string.IsNullOrWhiteSpace(password))
                return false;

            if (password.Length < 6)
                return false;

            if (!requireComplexity)
                return true;

            // Mot de passe complexe: au moins 8 caractères, 1 majuscule, 1 minuscule, 1 chiffre
            if (password.Length < 8)
                return false;

            bool hasUpper = password.Any(char.IsUpper);
            bool hasLower = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);

            return hasUpper && hasLower && hasDigit;
        }

        // Validation des fichiers
        public static bool IsValidImageFile(string? filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            var validExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff" };
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            
            return validExtensions.Contains(extension);
        }

        public static bool IsValidDocumentFile(string? filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            var validExtensions = new[] { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt", ".rtf" };
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            
            return validExtensions.Contains(extension);
        }

        // Méthodes de nettoyage
        public static string CleanString(string? value)
        {
            return value?.Trim() ?? string.Empty;
        }

        public static string CleanPhoneNumber(string? phone)
        {
            if (string.IsNullOrWhiteSpace(phone))
                return string.Empty;

            return Regex.Replace(phone, @"[^\d+]", "");
        }

        public static string FormatPhoneNumber(string? phone)
        {
            var cleaned = CleanPhoneNumber(phone);
            
            if (cleaned.Length == 10 && cleaned.StartsWith("0"))
            {
                // Format français: 01 23 45 67 89
                return $"{cleaned.Substring(0, 2)} {cleaned.Substring(2, 2)} {cleaned.Substring(4, 2)} {cleaned.Substring(6, 2)} {cleaned.Substring(8, 2)}";
            }

            return cleaned;
        }

        public static string FormatPostalCode(string? postalCode)
        {
            if (string.IsNullOrWhiteSpace(postalCode))
                return string.Empty;

            return postalCode.Trim().ToUpperInvariant();
        }

        // Validation métier spécifique
        public static bool IsValidStockQuantity(decimal quantity)
        {
            return quantity >= 0 && quantity <= 999999.99m;
        }

        public static bool IsValidPrice(decimal price)
        {
            return price >= 0 && price <= 999999.99m;
        }

        public static bool IsValidDiscount(decimal discount)
        {
            return discount >= 0 && discount <= 100;
        }

        public static bool IsValidTaxRate(decimal taxRate)
        {
            return taxRate >= 0 && taxRate <= 100;
        }

        public static bool IsValidCreditLimit(decimal creditLimit)
        {
            return creditLimit >= 0 && creditLimit <= 9999999.99m;
        }

        public static bool IsValidPaymentDelay(int days)
        {
            return days >= 0 && days <= 365;
        }
    }
}

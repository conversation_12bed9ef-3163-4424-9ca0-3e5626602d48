using System;
using System.Collections.Generic;

namespace SalesManagementSystem.Models
{
    public class Devis : BaseEntity
    {
        public string NumeroDevis { get; set; } = string.Empty;
        public DateTime DateDevis { get; set; } = DateTime.Now;
        public DateTime DateValidite { get; set; }
        public int ClientId { get; set; }
        public StatutDevis Statut { get; set; } = StatutDevis.Brouillon;
        
        // Montants
        public decimal MontantHT { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal MontantRemise { get; set; }
        public decimal PourcentageRemise { get; set; }
        public decimal MontantTimbre { get; set; }
        
        // Informations complémentaires
        public string? Remarques { get; set; }
        public string? ConditionsVente { get; set; }
        public string? DelaiLivraison { get; set; }
        public string? LieuLivraison { get; set; }
        public string? ValiditeOffre { get; set; }
        
        // Conversion
        public bool EstConvertiEnFacture { get; set; } = false;
        public DateTime? DateConversion { get; set; }
        public int? FactureId { get; set; }
        
        // Navigation properties
        public Client? Client { get; set; }
        public List<LigneDevis> Lignes { get; set; } = new List<LigneDevis>();
        public Facture? Facture { get; set; }
    }

    public class LigneDevis : BaseEntity
    {
        public int DevisId { get; set; }
        public int ProduitId { get; set; }
        public string DesignationProduit { get; set; } = string.Empty;
        public decimal Quantite { get; set; }
        public string Unite { get; set; } = string.Empty;
        public decimal PrixUnitaire { get; set; }
        public decimal PrixUnitaireHT { get; set; }
        public decimal MontantHT { get; set; }
        public decimal TauxTVA { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal PourcentageRemise { get; set; }
        public decimal MontantRemise { get; set; }
        
        // Navigation properties
        public Devis? Devis { get; set; }
        public Produit? Produit { get; set; }
    }

    public enum StatutDevis
    {
        Brouillon = 1,
        Envoye = 2,
        Accepte = 3,
        Refuse = 4,
        Expire = 5,
        Annule = 6,
        Converti = 7
    }

    // Facture Proforma
    public class FactureProforma : BaseEntity
    {
        public string NumeroProforma { get; set; } = string.Empty;
        public DateTime DateProforma { get; set; } = DateTime.Now;
        public DateTime DateValidite { get; set; }
        public int ClientId { get; set; }
        public StatutProforma Statut { get; set; } = StatutProforma.Brouillon;
        
        // Montants
        public decimal MontantHT { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal MontantRemise { get; set; }
        public decimal PourcentageRemise { get; set; }
        public decimal MontantTimbre { get; set; }
        
        // Informations complémentaires
        public string? Remarques { get; set; }
        public string? ConditionsVente { get; set; }
        public string? DelaiLivraison { get; set; }
        public string? LieuLivraison { get; set; }
        
        // Conversion
        public bool EstConvertiEnFacture { get; set; } = false;
        public DateTime? DateConversion { get; set; }
        public int? FactureId { get; set; }
        
        // Navigation properties
        public Client? Client { get; set; }
        public List<LigneFactureProforma> Lignes { get; set; } = new List<LigneFactureProforma>();
        public Facture? Facture { get; set; }
    }

    public class LigneFactureProforma : BaseEntity
    {
        public int FactureProformaId { get; set; }
        public int ProduitId { get; set; }
        public string DesignationProduit { get; set; } = string.Empty;
        public decimal Quantite { get; set; }
        public string Unite { get; set; } = string.Empty;
        public decimal PrixUnitaire { get; set; }
        public decimal PrixUnitaireHT { get; set; }
        public decimal MontantHT { get; set; }
        public decimal TauxTVA { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal PourcentageRemise { get; set; }
        public decimal MontantRemise { get; set; }
        
        // Navigation properties
        public FactureProforma? FactureProforma { get; set; }
        public Produit? Produit { get; set; }
    }

    public enum StatutProforma
    {
        Brouillon = 1,
        Envoye = 2,
        Accepte = 3,
        Refuse = 4,
        Expire = 5,
        Annule = 6,
        Converti = 7
    }
}

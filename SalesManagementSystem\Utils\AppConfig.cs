using System;
using System.Configuration;
using System.IO;

namespace SalesManagementSystem.Utils
{
    public static class AppConfig
    {
        // Informations de l'application
        public static string AppName => GetSetting("AppName", "Système de Gestion des Ventes et Stocks");
        public static string AppVersion => GetSetting("AppVersion", "1.0.0");
        public static string Company => GetSetting("Company", "EE Solutions");

        // Configuration de la base de données
        public static string DatabasePath => GetSetting("DatabasePath", "Data\\SalesManagement.db");
        public static string BackupPath => GetSetting("BackupPath", "Backups\\");
        public static bool AutoBackup => GetBoolSetting("AutoBackup", true);
        public static int BackupInterval => GetIntSetting("BackupInterval", 24);

        // Configuration de sécurité
        public static bool PasswordComplexity => GetBoolSetting("PasswordComplexity", true);
        public static int PasswordExpiry => GetIntSetting("PasswordExpiry", 90);
        public static int MaxLoginAttempts => GetIntSetting("MaxLoginAttempts", 3);
        public static int LockoutDuration => GetIntSetting("LockoutDuration", 30);

        // Configuration régionale
        public static string Culture => GetSetting("Culture", "fr-FR");
        public static string Currency => GetSetting("Currency", "€");
        public static string DateFormat => GetSetting("DateFormat", "dd/MM/yyyy");
        public static string NumberFormat => GetSetting("NumberFormat", "N2");

        // Configuration des rapports
        public static string ReportsPath => GetSetting("ReportsPath", "Reports\\");
        public static string ExportsPath => GetSetting("ExportsPath", "Exports\\");
        public static string DefaultPDFViewer => GetSetting("DefaultPDFViewer", "");

        // Configuration de l'interface
        public static string Theme => GetSetting("Theme", "Light");
        public static string FontFamily => GetSetting("FontFamily", "Segoe UI");
        public static int FontSize => GetIntSetting("FontSize", 10);
        public static bool EnableTouch => GetBoolSetting("EnableTouch", true);

        // Configuration des logs
        public static string LogLevel => GetSetting("LogLevel", "Information");
        public static string LogPath => GetSetting("LogPath", "Logs\\");
        public static int LogRetentionDays => GetIntSetting("LogRetentionDays", 30);

        // Configuration des notifications
        public static bool ShowStockAlerts => GetBoolSetting("ShowStockAlerts", true);
        public static bool ShowPaymentReminders => GetBoolSetting("ShowPaymentReminders", true);
        public static bool ShowExpiryAlerts => GetBoolSetting("ShowExpiryAlerts", true);

        // Configuration des impressions
        public static string DefaultPrinter => GetSetting("DefaultPrinter", "");
        public static bool PrintPreview => GetBoolSetting("PrintPreview", true);
        public static int PrintCopies => GetIntSetting("PrintCopies", 1);

        // Configuration des exports
        public static string ExcelTemplate => GetSetting("ExcelTemplate", "Templates\\Invoice.xlsx");
        public static string PDFTemplate => GetSetting("PDFTemplate", "Templates\\Invoice.pdf");
        public static bool AutoOpenExports => GetBoolSetting("AutoOpenExports", true);

        // Méthodes utilitaires
        private static string GetSetting(string key, string defaultValue = "")
        {
            try
            {
                return ConfigurationManager.AppSettings[key] ?? defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        private static bool GetBoolSetting(string key, bool defaultValue = false)
        {
            try
            {
                var value = ConfigurationManager.AppSettings[key];
                return bool.TryParse(value, out bool result) ? result : defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        private static int GetIntSetting(string key, int defaultValue = 0)
        {
            try
            {
                var value = ConfigurationManager.AppSettings[key];
                return int.TryParse(value, out int result) ? result : defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        private static decimal GetDecimalSetting(string key, decimal defaultValue = 0)
        {
            try
            {
                var value = ConfigurationManager.AppSettings[key];
                return decimal.TryParse(value, out decimal result) ? result : defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        // Méthodes pour créer les répertoires nécessaires
        public static void EnsureDirectoriesExist()
        {
            var directories = new[]
            {
                Path.GetDirectoryName(DatabasePath),
                BackupPath,
                ReportsPath,
                ExportsPath,
                LogPath,
                "Templates",
                "Temp"
            };

            foreach (var directory in directories)
            {
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    try
                    {
                        Directory.CreateDirectory(directory);
                    }
                    catch (Exception ex)
                    {
                        // Log l'erreur mais continue
                        System.Diagnostics.Debug.WriteLine($"Erreur lors de la création du répertoire {directory}: {ex.Message}");
                    }
                }
            }
        }

        // Méthode pour obtenir le chemin complet d'un fichier
        public static string GetFullPath(string relativePath)
        {
            if (Path.IsPathRooted(relativePath))
                return relativePath;

            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, relativePath);
        }

        // Méthode pour obtenir la chaîne de connexion
        public static string GetConnectionString(string name = "DefaultConnection")
        {
            try
            {
                return ConfigurationManager.ConnectionStrings[name]?.ConnectionString ?? "";
            }
            catch
            {
                return "";
            }
        }

        // Méthode pour sauvegarder une configuration
        public static void SaveSetting(string key, string value)
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                if (config.AppSettings.Settings[key] != null)
                {
                    config.AppSettings.Settings[key].Value = value;
                }
                else
                {
                    config.AppSettings.Settings.Add(key, value);
                }
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors de la sauvegarde de la configuration {key}: {ex.Message}");
            }
        }

        // Méthode pour obtenir les informations système
        public static class SystemInfo
        {
            public static string OSVersion => Environment.OSVersion.ToString();
            public static string MachineName => Environment.MachineName;
            public static string UserName => Environment.UserName;
            public static string WorkingDirectory => Environment.CurrentDirectory;
            public static string ApplicationPath => AppDomain.CurrentDomain.BaseDirectory;
            public static bool Is64BitOS => Environment.Is64BitOperatingSystem;
            public static bool Is64BitProcess => Environment.Is64BitProcess;
            public static int ProcessorCount => Environment.ProcessorCount;
            public static long WorkingSet => Environment.WorkingSet;
        }
    }
}

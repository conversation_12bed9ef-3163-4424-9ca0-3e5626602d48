@echo off
echo ========================================
echo   Publication du Système de Gestion
echo   des Ventes et Stocks - EE Solutions
echo ========================================
echo.

REM Nettoyer les anciens builds
echo Nettoyage des anciens builds...
dotnet clean SalesManagementSystem.sln --configuration Release
if errorlevel 1 goto error

REM Restaurer les packages
echo Restauration des packages NuGet...
dotnet restore SalesManagementSystem.sln
if errorlevel 1 goto error

REM Compiler en mode Release
echo Compilation en mode Release...
dotnet build SalesManagementSystem.sln --configuration Release --no-restore
if errorlevel 1 goto error

REM Publier l'application
echo Publication de l'application...
dotnet publish SalesManagementSystem\SalesManagementSystem.csproj ^
    --configuration Release ^
    --output ".\Publish\SalesManagementSystem" ^
    --self-contained false ^
    --runtime win-x64 ^
    --verbosity minimal
if errorlevel 1 goto error

REM Créer le dossier de distribution
echo Création du package de distribution...
if not exist ".\Distribution" mkdir ".\Distribution"
if exist ".\Distribution\SalesManagementSystem" rmdir /s /q ".\Distribution\SalesManagementSystem"
mkdir ".\Distribution\SalesManagementSystem"

REM Copier les fichiers publiés
xcopy ".\Publish\SalesManagementSystem\*" ".\Distribution\SalesManagementSystem\" /E /I /Y
if errorlevel 1 goto error

REM Copier les fichiers de documentation
copy "README.md" ".\Distribution\SalesManagementSystem\README.md"
copy "GUIDE_UTILISATION.md" ".\Distribution\SalesManagementSystem\GUIDE_UTILISATION.md"

REM Créer les dossiers nécessaires
mkdir ".\Distribution\SalesManagementSystem\Data" 2>nul
mkdir ".\Distribution\SalesManagementSystem\Logs" 2>nul
mkdir ".\Distribution\SalesManagementSystem\Backups" 2>nul
mkdir ".\Distribution\SalesManagementSystem\Exports" 2>nul
mkdir ".\Distribution\SalesManagementSystem\Reports" 2>nul
mkdir ".\Distribution\SalesManagementSystem\Templates" 2>nul

REM Créer un fichier de démarrage
echo @echo off > ".\Distribution\SalesManagementSystem\Demarrer.bat"
echo echo Démarrage du Système de Gestion des Ventes et Stocks... >> ".\Distribution\SalesManagementSystem\Demarrer.bat"
echo SalesManagementSystem.exe >> ".\Distribution\SalesManagementSystem\Demarrer.bat"
echo pause >> ".\Distribution\SalesManagementSystem\Demarrer.bat"

REM Créer un fichier d'informations
echo Système de Gestion des Ventes et Stocks > ".\Distribution\SalesManagementSystem\VERSION.txt"
echo Version 1.0.0 >> ".\Distribution\SalesManagementSystem\VERSION.txt"
echo Développé par EE Solutions >> ".\Distribution\SalesManagementSystem\VERSION.txt"
echo Date de compilation: %date% %time% >> ".\Distribution\SalesManagementSystem\VERSION.txt"
echo. >> ".\Distribution\SalesManagementSystem\VERSION.txt"
echo Prérequis: >> ".\Distribution\SalesManagementSystem\VERSION.txt"
echo - Windows 7 ou supérieur >> ".\Distribution\SalesManagementSystem\VERSION.txt"
echo - .NET 6.0 Runtime >> ".\Distribution\SalesManagementSystem\VERSION.txt"
echo. >> ".\Distribution\SalesManagementSystem\VERSION.txt"
echo Identifiants par défaut: >> ".\Distribution\SalesManagementSystem\VERSION.txt"
echo Utilisateur: admin >> ".\Distribution\SalesManagementSystem\VERSION.txt"
echo Mot de passe: admin123 >> ".\Distribution\SalesManagementSystem\VERSION.txt"

echo.
echo ========================================
echo   Publication terminée avec succès !
echo ========================================
echo.
echo L'application est disponible dans le dossier:
echo .\Distribution\SalesManagementSystem\
echo.
echo Pour démarrer l'application:
echo 1. Assurez-vous que .NET 6.0 Runtime est installé
echo 2. Exécutez SalesManagementSystem.exe
echo    ou utilisez Demarrer.bat
echo.
echo Identifiants par défaut:
echo Utilisateur: admin
echo Mot de passe: admin123
echo.
pause
goto end

:error
echo.
echo ========================================
echo   ERREUR lors de la publication !
echo ========================================
echo.
echo Vérifiez les messages d'erreur ci-dessus
echo et corrigez les problèmes avant de relancer.
echo.
pause
exit /b 1

:end
exit /b 0

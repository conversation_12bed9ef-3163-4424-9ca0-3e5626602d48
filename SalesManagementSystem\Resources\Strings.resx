<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Général -->
  <data name="AppTitle" xml:space="preserve">
    <value>Système de Gestion des Ventes et Stocks</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>Version 1.0.0</value>
  </data>
  
  <!-- Connexion -->
  <data name="Login" xml:space="preserve">
    <value>Connexion</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="Connect" xml:space="preserve">
    <value>Se connecter</value>
  </data>
  <data name="Quit" xml:space="preserve">
    <value>Quitter</value>
  </data>
  <data name="ShowPassword" xml:space="preserve">
    <value>Afficher le mot de passe</value>
  </data>
  
  <!-- Menus -->
  <data name="File" xml:space="preserve">
    <value>Fichier</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>Ventes</value>
  </data>
  <data name="Purchases" xml:space="preserve">
    <value>Achats</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Stock</value>
  </data>
  <data name="Accounting" xml:space="preserve">
    <value>Comptabilité</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Rapports</value>
  </data>
  <data name="Help" xml:space="preserve">
    <value>Aide</value>
  </data>
  
  <!-- Factures -->
  <data name="Invoice" xml:space="preserve">
    <value>Facture</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Factures</value>
  </data>
  <data name="NewInvoice" xml:space="preserve">
    <value>Nouvelle Facture</value>
  </data>
  <data name="InvoiceNumber" xml:space="preserve">
    <value>Numéro de Facture</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Date de Facture</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Date d'Échéance</value>
  </data>
  
  <!-- Devis -->
  <data name="Quote" xml:space="preserve">
    <value>Devis</value>
  </data>
  <data name="Quotes" xml:space="preserve">
    <value>Devis</value>
  </data>
  <data name="NewQuote" xml:space="preserve">
    <value>Nouveau Devis</value>
  </data>
  <data name="QuoteNumber" xml:space="preserve">
    <value>Numéro de Devis</value>
  </data>
  <data name="QuoteDate" xml:space="preserve">
    <value>Date de Devis</value>
  </data>
  <data name="ValidityDate" xml:space="preserve">
    <value>Date de Validité</value>
  </data>
  
  <!-- Clients -->
  <data name="Client" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="Clients" xml:space="preserve">
    <value>Clients</value>
  </data>
  <data name="ClientCode" xml:space="preserve">
    <value>Code Client</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Raison Sociale</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Prénom</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>Code Postal</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Pays</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>Téléphone</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  
  <!-- Fournisseurs -->
  <data name="Supplier" xml:space="preserve">
    <value>Fournisseur</value>
  </data>
  <data name="Suppliers" xml:space="preserve">
    <value>Fournisseurs</value>
  </data>
  <data name="SupplierCode" xml:space="preserve">
    <value>Code Fournisseur</value>
  </data>
  
  <!-- Produits -->
  <data name="Product" xml:space="preserve">
    <value>Produit</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Produits</value>
  </data>
  <data name="ProductCode" xml:space="preserve">
    <value>Code Produit</value>
  </data>
  <data name="Designation" xml:space="preserve">
    <value>Désignation</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Catégorie</value>
  </data>
  <data name="SubCategory" xml:space="preserve">
    <value>Sous-Catégorie</value>
  </data>
  <data name="PurchasePrice" xml:space="preserve">
    <value>Prix d'Achat</value>
  </data>
  <data name="SalePrice" xml:space="preserve">
    <value>Prix de Vente</value>
  </data>
  <data name="WholesalePrice" xml:space="preserve">
    <value>Prix de Gros</value>
  </data>
  <data name="SemiWholesalePrice" xml:space="preserve">
    <value>Prix Demi-Gros</value>
  </data>
  <data name="PriceHT" xml:space="preserve">
    <value>Prix HT</value>
  </data>
  <data name="PMP" xml:space="preserve">
    <value>PMP</value>
  </data>
  
  <!-- Stock -->
  <data name="InitialStock" xml:space="preserve">
    <value>Stock Initial</value>
  </data>
  <data name="CurrentStock" xml:space="preserve">
    <value>Stock Actuel</value>
  </data>
  <data name="MinimumStock" xml:space="preserve">
    <value>Stock Minimum</value>
  </data>
  <data name="MaximumStock" xml:space="preserve">
    <value>Stock Maximum</value>
  </data>
  <data name="ReservedStock" xml:space="preserve">
    <value>Stock Réservé</value>
  </data>
  
  <!-- Unités -->
  <data name="Unit" xml:space="preserve">
    <value>Unité</value>
  </data>
  <data name="Piece" xml:space="preserve">
    <value>Pièce</value>
  </data>
  <data name="Box" xml:space="preserve">
    <value>Carton</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Colis</value>
  </data>
  
  <!-- Montants -->
  <data name="Amount" xml:space="preserve">
    <value>Montant</value>
  </data>
  <data name="AmountHT" xml:space="preserve">
    <value>Montant HT</value>
  </data>
  <data name="AmountTTC" xml:space="preserve">
    <value>Montant TTC</value>
  </data>
  <data name="VAT" xml:space="preserve">
    <value>TVA</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>Remise</value>
  </data>
  <data name="Stamp" xml:space="preserve">
    <value>Timbre</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  
  <!-- Actions -->
  <data name="Add" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Rechercher</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Imprimer</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Exporter</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Importer</value>
  </data>
  
  <!-- Messages -->
  <data name="Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Attention</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="Confirmation" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Succès</value>
  </data>
  
  <!-- Statuts -->
  <data name="Active" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactif</value>
  </data>
  <data name="Draft" xml:space="preserve">
    <value>Brouillon</value>
  </data>
  <data name="Validated" xml:space="preserve">
    <value>Validé</value>
  </data>
  <data name="Sent" xml:space="preserve">
    <value>Envoyé</value>
  </data>
  <data name="Paid" xml:space="preserve">
    <value>Payé</value>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>Annulé</value>
  </data>
</root>

<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <AssemblyTitle>Système de Gestion des Ventes et Stocks</AssemblyTitle>
    <AssemblyDescription>Système professionnel de gestion des ventes et stocks</AssemblyDescription>
    <AssemblyCompany>EE Solutions</AssemblyCompany>
    <AssemblyProduct>Sales Management System</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024 EE Solutions</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.24" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="8.0.0" />
    <PackageReference Include="iTextSharp" Version="********" />
    <PackageReference Include="EPPlus" Version="7.0.4" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
    <Folder Include="Forms\" />
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Data\" />
    <Folder Include="Utils\" />
    <Folder Include="Reports\" />
  </ItemGroup>



</Project>

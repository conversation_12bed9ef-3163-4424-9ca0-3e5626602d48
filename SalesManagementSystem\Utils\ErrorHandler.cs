using System;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace SalesManagementSystem.Utils
{
    public static class ErrorHandler
    {
        private static readonly string LogPath = Path.Combine(AppConfig.LogPath, "errors.log");

        public static void LogError(Exception ex, string context = "")
        {
            try
            {
                var logEntry = CreateLogEntry(ex, context);
                WriteToLogFile(logEntry);
            }
            catch
            {
                // Si on ne peut pas logger, on ne fait rien pour éviter une boucle infinie
            }
        }

        public static void LogError(string message, string context = "")
        {
            try
            {
                var logEntry = CreateLogEntry(message, context);
                WriteToLogFile(logEntry);
            }
            catch
            {
                // Si on ne peut pas logger, on ne fait rien pour éviter une boucle infinie
            }
        }

        public static void ShowError(Exception ex, string title = "Erreur")
        {
            LogError(ex, title);
            
            var message = GetUserFriendlyMessage(ex);
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        public static void ShowError(string message, string title = "Erreur")
        {
            LogError(message, title);
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        public static void ShowWarning(string message, string title = "Attention")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        public static void ShowInfo(string message, string title = "Information")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        public static bool ShowConfirmation(string message, string title = "Confirmation")
        {
            var result = MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            return result == DialogResult.Yes;
        }

        public static DialogResult ShowQuestion(string message, string title = "Question")
        {
            return MessageBox.Show(message, title, MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
        }

        private static string CreateLogEntry(Exception ex, string context)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERREUR");
            
            if (!string.IsNullOrEmpty(context))
                sb.AppendLine($"Contexte: {context}");
            
            sb.AppendLine($"Type: {ex.GetType().Name}");
            sb.AppendLine($"Message: {ex.Message}");
            
            if (ex.InnerException != null)
            {
                sb.AppendLine($"Exception interne: {ex.InnerException.Message}");
            }
            
            sb.AppendLine($"Stack Trace: {ex.StackTrace}");
            sb.AppendLine($"Source: {ex.Source}");
            sb.AppendLine($"Utilisateur: {Environment.UserName}");
            sb.AppendLine($"Machine: {Environment.MachineName}");
            sb.AppendLine(new string('-', 80));
            
            return sb.ToString();
        }

        private static string CreateLogEntry(string message, string context)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] MESSAGE");
            
            if (!string.IsNullOrEmpty(context))
                sb.AppendLine($"Contexte: {context}");
            
            sb.AppendLine($"Message: {message}");
            sb.AppendLine($"Utilisateur: {Environment.UserName}");
            sb.AppendLine($"Machine: {Environment.MachineName}");
            sb.AppendLine(new string('-', 80));
            
            return sb.ToString();
        }

        private static void WriteToLogFile(string logEntry)
        {
            try
            {
                // Créer le répertoire si nécessaire
                var directory = Path.GetDirectoryName(LogPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                // Écrire dans le fichier de log
                File.AppendAllText(LogPath, logEntry, Encoding.UTF8);

                // Nettoyer les anciens logs si nécessaire
                CleanOldLogs();
            }
            catch
            {
                // Si on ne peut pas écrire dans le fichier, on essaie le répertoire temp
                try
                {
                    var tempLogPath = Path.Combine(Path.GetTempPath(), "SalesManagement_errors.log");
                    File.AppendAllText(tempLogPath, logEntry, Encoding.UTF8);
                }
                catch
                {
                    // Dernière tentative: écrire dans la console de debug
                    System.Diagnostics.Debug.WriteLine(logEntry);
                }
            }
        }

        private static void CleanOldLogs()
        {
            try
            {
                var logDirectory = Path.GetDirectoryName(LogPath);
                if (!Directory.Exists(logDirectory)) return;

                var files = Directory.GetFiles(logDirectory, "*.log");
                var cutoffDate = DateTime.Now.AddDays(-AppConfig.LogRetentionDays);

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // Ignorer les erreurs de suppression
                        }
                    }
                }
            }
            catch
            {
                // Ignorer les erreurs de nettoyage
            }
        }

        private static string GetUserFriendlyMessage(Exception ex)
        {
            return ex switch
            {
                UnauthorizedAccessException => "Vous n'avez pas les permissions nécessaires pour effectuer cette opération.",
                FileNotFoundException => "Le fichier demandé est introuvable.",
                DirectoryNotFoundException => "Le répertoire spécifié est introuvable.",
                IOException => "Une erreur d'entrée/sortie s'est produite.",
                ArgumentException => "Un paramètre invalide a été fourni.",
                InvalidOperationException => "Cette opération n'est pas valide dans le contexte actuel.",
                NotSupportedException => "Cette opération n'est pas supportée.",
                TimeoutException => "L'opération a expiré. Veuillez réessayer.",
                OutOfMemoryException => "Mémoire insuffisante pour effectuer cette opération.",
                _ => $"Une erreur inattendue s'est produite: {ex.Message}"
            };
        }

        public static class DatabaseErrors
        {
            public static void HandleConnectionError(Exception ex)
            {
                LogError(ex, "Erreur de connexion à la base de données");
                ShowError("Impossible de se connecter à la base de données. Vérifiez que le fichier de base de données existe et que vous avez les permissions nécessaires.", "Erreur de Base de Données");
            }

            public static void HandleQueryError(Exception ex, string query = "")
            {
                LogError(ex, $"Erreur lors de l'exécution de la requête: {query}");
                ShowError("Une erreur s'est produite lors de l'accès aux données. Veuillez réessayer ou contacter l'administrateur.", "Erreur de Base de Données");
            }

            public static void HandleTransactionError(Exception ex)
            {
                LogError(ex, "Erreur de transaction");
                ShowError("La transaction n'a pas pu être complétée. Les modifications ont été annulées.", "Erreur de Transaction");
            }
        }

        public static class ValidationErrors
        {
            public static void ShowRequiredFieldError(string fieldName)
            {
                ShowWarning($"Le champ '{fieldName}' est obligatoire.", "Champ Obligatoire");
            }

            public static void ShowInvalidFormatError(string fieldName, string expectedFormat)
            {
                ShowWarning($"Le format du champ '{fieldName}' est invalide. Format attendu: {expectedFormat}", "Format Invalide");
            }

            public static void ShowDuplicateError(string fieldName, string value)
            {
                ShowWarning($"La valeur '{value}' existe déjà pour le champ '{fieldName}'.", "Valeur Dupliquée");
            }

            public static void ShowRangeError(string fieldName, string minValue, string maxValue)
            {
                ShowWarning($"La valeur du champ '{fieldName}' doit être comprise entre {minValue} et {maxValue}.", "Valeur Hors Limites");
            }
        }

        public static class BusinessErrors
        {
            public static void ShowInsufficientStockError(string productName, decimal available, decimal requested)
            {
                ShowWarning($"Stock insuffisant pour le produit '{productName}'. Stock disponible: {available}, Quantité demandée: {requested}", "Stock Insuffisant");
            }

            public static void ShowCreditLimitExceededError(string clientName, decimal limit, decimal amount)
            {
                ShowWarning($"Limite de crédit dépassée pour le client '{clientName}'. Limite: {limit:C}, Montant: {amount:C}", "Limite de Crédit Dépassée");
            }

            public static void ShowInvalidDateRangeError()
            {
                ShowWarning("La date de fin doit être postérieure à la date de début.", "Période Invalide");
            }

            public static void ShowDocumentAlreadyValidatedError(string documentType, string documentNumber)
            {
                ShowWarning($"Le {documentType} {documentNumber} est déjà validé et ne peut plus être modifié.", "Document Validé");
            }
        }
    }
}

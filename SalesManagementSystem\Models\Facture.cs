using System;
using System.Collections.Generic;

namespace SalesManagementSystem.Models
{
    public class Facture : BaseEntity
    {
        public string NumeroFacture { get; set; } = string.Empty;
        public DateTime DateFacture { get; set; } = DateTime.Now;
        public DateTime DateEcheance { get; set; }
        public int ClientId { get; set; }
        public TypeFacture TypeFacture { get; set; }
        public StatutFacture Statut { get; set; } = StatutFacture.Brouillon;
        
        // Montants
        public decimal MontantHT { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal MontantRemise { get; set; }
        public decimal PourcentageRemise { get; set; }
        public decimal MontantTimbre { get; set; }
        public decimal MontantPaye { get; set; }
        public decimal MontantRestant { get; set; }
        
        // Informations complémentaires
        public string? Remarques { get; set; }
        public string? ConditionsPaiement { get; set; }
        public string? ModePaiement { get; set; }
        public bool EstPayee { get; set; } = false;
        public DateTime? DatePaiement { get; set; }
        
        // Référence devis/proforma
        public int? DevisId { get; set; }
        public int? FactureProformaId { get; set; }
        
        // Navigation properties
        public Client? Client { get; set; }
        public List<LigneFacture> Lignes { get; set; } = new List<LigneFacture>();
        public List<PaiementFacture> Paiements { get; set; } = new List<PaiementFacture>();
        public Devis? Devis { get; set; }
        public FactureProforma? FactureProforma { get; set; }
    }

    public class LigneFacture : BaseEntity
    {
        public int FactureId { get; set; }
        public int ProduitId { get; set; }
        public string DesignationProduit { get; set; } = string.Empty;
        public decimal Quantite { get; set; }
        public string Unite { get; set; } = string.Empty;
        public decimal PrixUnitaire { get; set; }
        public decimal PrixUnitaireHT { get; set; }
        public decimal MontantHT { get; set; }
        public decimal TauxTVA { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal PourcentageRemise { get; set; }
        public decimal MontantRemise { get; set; }
        
        // Navigation properties
        public Facture? Facture { get; set; }
        public Produit? Produit { get; set; }
    }

    public class PaiementFacture : BaseEntity
    {
        public int FactureId { get; set; }
        public DateTime DatePaiement { get; set; } = DateTime.Now;
        public decimal Montant { get; set; }
        public ModePaiement ModePaiement { get; set; }
        public string? Reference { get; set; }
        public string? Remarques { get; set; }
        
        // Navigation properties
        public Facture? Facture { get; set; }
    }

    public enum TypeFacture
    {
        Vente = 1,
        RetourVente = 2,
        Avoir = 3
    }

    public enum StatutFacture
    {
        Brouillon = 1,
        Validee = 2,
        Envoyee = 3,
        Payee = 4,
        Annulee = 5,
        Retournee = 6
    }

    public enum ModePaiement
    {
        Especes = 1,
        Cheque = 2,
        CarteCredit = 3,
        Virement = 4,
        Prelevement = 5,
        TraiteEffet = 6,
        Compensation = 7
    }
}

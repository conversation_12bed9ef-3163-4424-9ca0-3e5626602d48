using System;
using System.Collections.Generic;

namespace SalesManagementSystem.Models
{
    public class CompteComptable : BaseEntity
    {
        public string NumeroCompte { get; set; } = string.Empty;
        public string Libelle { get; set; } = string.Empty;
        public TypeCompte TypeCompte { get; set; }
        public int? CompteParentId { get; set; }
        public decimal SoldeInitial { get; set; }
        public decimal SoldeActuel { get; set; }
        public bool EstActif { get; set; } = true;
        public string? Description { get; set; }
        
        // Navigation properties
        public CompteComptable? CompteParent { get; set; }
        public List<CompteComptable> SousComptes { get; set; } = new List<CompteComptable>();
        public List<EcritureComptable> Ecritures { get; set; } = new List<EcritureComptable>();
    }

    public class EcritureComptable : BaseEntity
    {
        public string NumeroEcriture { get; set; } = string.Empty;
        public DateTime DateEcriture { get; set; } = DateTime.Now;
        public string Libelle { get; set; } = string.Empty;
        public string? Reference { get; set; }
        public string? PieceJustificative { get; set; }
        public bool EstValidee { get; set; } = false;
        public DateTime? DateValidation { get; set; }
        
        // Navigation properties
        public List<LigneEcriture> Lignes { get; set; } = new List<LigneEcriture>();
    }

    public class LigneEcriture : BaseEntity
    {
        public int EcritureComptableId { get; set; }
        public int CompteComptableId { get; set; }
        public string Libelle { get; set; } = string.Empty;
        public decimal Debit { get; set; }
        public decimal Credit { get; set; }
        public string? Remarques { get; set; }
        
        // Navigation properties
        public EcritureComptable? EcritureComptable { get; set; }
        public CompteComptable? CompteComptable { get; set; }
    }

    public class OperationCaisse : BaseEntity
    {
        public DateTime DateOperation { get; set; } = DateTime.Now;
        public TypeOperationCaisse TypeOperation { get; set; }
        public decimal Montant { get; set; }
        public string Libelle { get; set; } = string.Empty;
        public string? Reference { get; set; }
        public ModePaiement ModePaiement { get; set; }
        public string? Remarques { get; set; }
        
        // Références
        public int? ClientId { get; set; }
        public int? FournisseurId { get; set; }
        public int? FactureId { get; set; }
        public int? FactureAchatId { get; set; }
        
        // Navigation properties
        public Client? Client { get; set; }
        public Fournisseur? Fournisseur { get; set; }
        public Facture? Facture { get; set; }
        public FactureAchat? FactureAchat { get; set; }
    }

    public class Depense : BaseEntity
    {
        public string NumeroDepense { get; set; } = string.Empty;
        public DateTime DateDepense { get; set; } = DateTime.Now;
        public string Libelle { get; set; } = string.Empty;
        public decimal Montant { get; set; }
        public CategorieDepense CategorieDepense { get; set; }
        public ModePaiement ModePaiement { get; set; }
        public string? Beneficiaire { get; set; }
        public string? Reference { get; set; }
        public string? Remarques { get; set; }
        public bool EstValidee { get; set; } = false;
        
        // Pièces justificatives
        public string? CheminPieceJustificative { get; set; }
    }

    public class Recette : BaseEntity
    {
        public string NumeroRecette { get; set; } = string.Empty;
        public DateTime DateRecette { get; set; } = DateTime.Now;
        public string Libelle { get; set; } = string.Empty;
        public decimal Montant { get; set; }
        public CategorieRecette CategorieRecette { get; set; }
        public ModePaiement ModePaiement { get; set; }
        public string? Payeur { get; set; }
        public string? Reference { get; set; }
        public string? Remarques { get; set; }
        public bool EstValidee { get; set; } = false;
        
        // Pièces justificatives
        public string? CheminPieceJustificative { get; set; }
    }

    public enum TypeCompte
    {
        Actif = 1,
        Passif = 2,
        Charge = 3,
        Produit = 4,
        Resultat = 5
    }

    public enum TypeOperationCaisse
    {
        Entree = 1,
        Sortie = 2,
        Virement = 3
    }

    public enum CategorieDepense
    {
        Achat = 1,
        Salaire = 2,
        Loyer = 3,
        Electricite = 4,
        Telephone = 5,
        Transport = 6,
        Publicite = 7,
        Assurance = 8,
        Impots = 9,
        Maintenance = 10,
        Fournitures = 11,
        Autre = 99
    }

    public enum CategorieRecette
    {
        Vente = 1,
        Service = 2,
        Location = 3,
        Interet = 4,
        Subvention = 5,
        Autre = 99
    }
}

# Système de Gestion des Ventes et Stocks

## 📋 Description

Un système professionnel et moderne de gestion des ventes et stocks développé en C# avec WinForms. Ce logiciel offre une solution complète pour la gestion commerciale des entreprises avec une interface utilisateur moderne et intuitive en français.

## ✨ Fonctionnalités Principales

### 🛒 Gestion des Ventes
- **Factures de vente** avec numérotation automatique
- **Devis** et conversion en factures
- **Factures Proforma** pour les commandes
- **Avoirs** et gestion des retours
- **Gestion des remises** et promotions
- **Application automatique du timbre fiscal**

### 📦 Gestion des Achats
- **Factures d'achat** et bons de réception
- **Commandes d'achat** avec suivi
- **Retours d'achat** et avoirs fournisseurs
- **Gestion des délais de paiement**

### 📊 Gestion du Stock
- **Gestion multi-unités** (pièce, carton, fardo)
- **Stock initial et ajustements**
- **Mouvements de stock** automatiques
- **Inventaires** et écarts
- **Alertes stock minimum**
- **Produits réservés**
- **Gestion des packs** et bundles
- **Prix multiples** (Achat, Vente, Gros, Demi-gros, HT, PMP)

### 👥 Gestion Commerciale
- **Clients** avec historique complet
- **Fournisseurs** et contacts commerciaux
- **Gestion des crédits** et limites
- **Suivi des paiements**
- **Échéanciers** automatiques

### 💰 Comptabilité
- **Plan comptable** personnalisable
- **Écritures comptables** automatiques
- **Gestion de caisse**
- **Recettes et dépenses**
- **Bilan** et compte de résultat
- **Suivi des débiteurs/créditeurs**

### 📈 Rapports et Analyses
- **Chiffre d'affaires** par période
- **Ventes par produit/client**
- **État des stocks**
- **Mouvements de stock**
- **Analyses financières**
- **Export PDF/Excel**

## 🔧 Technologies Utilisées

- **Framework**: .NET 6.0 Windows Forms
- **Base de données**: SQLite (locale)
- **ORM**: Dapper
- **Sécurité**: BCrypt.Net pour le hachage des mots de passe
- **Rapports**: iTextSharp (PDF), EPPlus (Excel)
- **Logging**: Serilog
- **Architecture**: Repository Pattern + Services

## 🚀 Installation et Configuration

### Prérequis
- Windows 7 ou supérieur
- .NET 6.0 Runtime
- 100 MB d'espace disque libre

### Installation
1. Téléchargez la dernière version depuis les releases
2. Exécutez le fichier d'installation
3. Lancez l'application
4. Connectez-vous avec les identifiants par défaut :
   - **Utilisateur**: `admin`
   - **Mot de passe**: `admin123`

### Premier Démarrage
1. Configurez les informations de votre entreprise
2. Créez vos catégories de produits
3. Ajoutez vos fournisseurs et clients
4. Configurez vos produits avec les stocks initiaux
5. Commencez à utiliser le système !

## 👤 Gestion des Utilisateurs

Le système supporte plusieurs types d'utilisateurs avec des permissions granulaires :

- **Administrateur** : Accès complet
- **Gestionnaire** : Gestion commerciale et stock
- **Vendeur** : Ventes et consultation
- **Magasinier** : Gestion du stock uniquement
- **Comptable** : Comptabilité et rapports
- **Consultation** : Lecture seule

## 🎨 Interface Utilisateur

- **Design moderne** avec couleurs professionnelles
- **Interface tactile** compatible
- **Multi-écrans** supporté
- **Raccourcis clavier** intuitifs
- **Thème sombre/clair** (à venir)

## 🔒 Sécurité

- **Authentification** sécurisée avec BCrypt
- **Gestion des sessions** utilisateur
- **Permissions granulaires** par fonctionnalité
- **Audit trail** des modifications
- **Sauvegarde automatique** des données

## 📱 Compatibilité

- **Windows 7** et supérieur
- **Résolution minimale** : 1024x768
- **Écrans tactiles** supportés
- **Multi-moniteurs** compatible

## 🛠️ Développement

### Structure du Projet
```
SalesManagementSystem/
├── Models/           # Entités métier
├── Data/            # Accès aux données (Repository)
├── Services/        # Logique métier
├── Forms/           # Interface utilisateur
├── Resources/       # Ressources (images, traductions)
├── Utils/           # Utilitaires
└── Reports/         # Génération de rapports
```

### Compilation
```bash
dotnet build SalesManagementSystem.sln
dotnet run --project SalesManagementSystem
```

## 📄 Base de Données

Le système utilise SQLite pour une base de données locale avec les tables principales :
- Utilisateurs, Entreprise
- Clients, Fournisseurs
- Produits, Categories, SousCategories
- Factures, LignesFacture, Paiements
- Stock, MouvementsStock, Inventaires
- Comptabilité (Comptes, Écritures)

## 🔄 Sauvegarde et Restauration

- **Sauvegarde automatique** programmable
- **Export/Import** des données
- **Sauvegarde manuelle** à tout moment
- **Restauration** depuis fichier de sauvegarde

## 📞 Support

Pour toute question ou problème :
- Consultez la documentation intégrée
- Utilisez le menu Aide > Manuel d'utilisation
- Contactez le support technique

## 📝 Licence

Ce logiciel est développé par EE Solutions.
Tous droits réservés © 2024.

## 🔄 Mises à Jour

- **Vérification automatique** des mises à jour
- **Installation simplifiée** des nouvelles versions
- **Migration automatique** des données

## 🌟 Fonctionnalités Avancées

### Gestion des Packs
Créez des packs de produits avec prix spéciaux :
- Pack "Nettoyage" = Javel + Savon + Éponge
- Prix pack différent de la somme des composants
- Gestion automatique du stock des composants

### Prix Multiples
Chaque produit peut avoir plusieurs prix :
- Prix d'achat (coût)
- Prix de vente (détail)
- Prix de gros
- Prix demi-gros
- Prix HT (hors taxes)
- PMP (Prix Moyen Pondéré) calculé automatiquement

### Promotions
- Définition de périodes promotionnelles
- Prix spéciaux temporaires
- Application automatique selon les dates

### Multi-Codes-Barres
- Plusieurs codes-barres par produit
- Support EAN13, Code128, etc.
- Code principal et codes secondaires

---

**Développé avec ❤️ par EE Solutions**

using SalesManagementSystem.Data;
using SalesManagementSystem.Models;
using System;
using System.Threading.Tasks;

namespace SalesManagementSystem.Services
{
    public class AuthenticationService
    {
        private readonly IUtilisateurRepository _utilisateurRepository;
        private static Utilisateur? _currentUser;

        public AuthenticationService(IUtilisateurRepository utilisateurRepository)
        {
            _utilisateurRepository = utilisateurRepository;
        }

        public static Utilisateur? CurrentUser => _currentUser;

        public async Task<AuthenticationResult> LoginAsync(string nomUtilisateur, string motDePasse)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(nomUtilisateur) || string.IsNullOrWhiteSpace(motDePasse))
                {
                    return new AuthenticationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "Le nom d'utilisateur et le mot de passe sont requis."
                    };
                }

                var utilisateur = await _utilisateurRepository.GetByNomUtilisateurAsync(nomUtilisateur);
                if (utilisateur == null)
                {
                    return new AuthenticationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "Nom d'utilisateur ou mot de passe incorrect."
                    };
                }

                if (!utilisateur.EstActif)
                {
                    return new AuthenticationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "Ce compte utilisateur est désactivé."
                    };
                }

                var isValidPassword = await _utilisateurRepository.ValidateCredentialsAsync(nomUtilisateur, motDePasse);
                if (!isValidPassword)
                {
                    return new AuthenticationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "Nom d'utilisateur ou mot de passe incorrect."
                    };
                }

                // Mettre à jour la dernière connexion
                await _utilisateurRepository.UpdateLastLoginAsync(utilisateur.Id);

                // Définir l'utilisateur actuel
                _currentUser = utilisateur;

                return new AuthenticationResult
                {
                    IsSuccess = true,
                    User = utilisateur
                };
            }
            catch (Exception ex)
            {
                return new AuthenticationResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"Erreur lors de la connexion: {ex.Message}"
                };
            }
        }

        public void Logout()
        {
            _currentUser = null;
        }

        public bool IsLoggedIn()
        {
            return _currentUser != null;
        }

        public bool HasPermission(Permission permission)
        {
            if (_currentUser == null) return false;

            if (_currentUser.EstAdministrateur) return true;

            return permission switch
            {
                Permission.Voir => _currentUser.PeutVoir,
                Permission.Ajouter => _currentUser.PeutAjouter,
                Permission.Modifier => _currentUser.PeutModifier,
                Permission.Supprimer => _currentUser.PeutSupprimer,
                Permission.Imprimer => _currentUser.PeutImprimer,
                Permission.Exporter => _currentUser.PeutExporter,
                _ => false
            };
        }

        public bool IsAdministrator()
        {
            return _currentUser?.EstAdministrateur ?? false;
        }

        public async Task<bool> ChangePasswordAsync(string ancienMotDePasse, string nouveauMotDePasse)
        {
            if (_currentUser == null) return false;

            var isValidOldPassword = await _utilisateurRepository.ValidateCredentialsAsync(
                _currentUser.NomUtilisateur, ancienMotDePasse);

            if (!isValidOldPassword) return false;

            return await _utilisateurRepository.ChangePasswordAsync(_currentUser.Id, nouveauMotDePasse);
        }

        public static string GetCurrentUserName()
        {
            return _currentUser?.NomUtilisateur ?? "Système";
        }

        public static string GetCurrentUserFullName()
        {
            if (_currentUser == null) return "Utilisateur inconnu";
            return $"{_currentUser.Prenom} {_currentUser.Nom}";
        }
    }

    public class AuthenticationResult
    {
        public bool IsSuccess { get; set; }
        public string? ErrorMessage { get; set; }
        public Utilisateur? User { get; set; }
    }

    public enum Permission
    {
        Voir,
        Ajouter,
        Modifier,
        Supprimer,
        Imprimer,
        Exporter
    }
}

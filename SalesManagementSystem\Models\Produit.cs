using System;
using System.Collections.Generic;

namespace SalesManagementSystem.Models
{
    public class Produit : BaseEntity
    {
        public string CodeProduit { get; set; } = string.Empty;
        public string Designation { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int CategorieId { get; set; }
        public int? SousCategorieId { get; set; }
        public int FournisseurId { get; set; }
        
        // Prix
        public decimal PrixAchat { get; set; }
        public decimal PrixVente { get; set; }
        public decimal PrixGros { get; set; }
        public decimal PrixDemiGros { get; set; }
        public decimal PrixHT { get; set; }
        public decimal PMP { get; set; } // Prix Moyen Pondéré
        
        // Stock
        public decimal StockInitial { get; set; }
        public decimal StockActuel { get; set; }
        public decimal StockMinimum { get; set; }
        public decimal StockMaximum { get; set; }
        public decimal StockReserve { get; set; }
        
        // Unités
        public string UniteBase { get; set; } = "Pièce";
        public string? UniteGros { get; set; }
        public decimal? FacteurConversion { get; set; }
        
        // Autres propriétés
        public bool EstActif { get; set; } = true;
        public bool EstEnPromotion { get; set; } = false;
        public decimal? PrixPromotion { get; set; }
        public DateTime? DateDebutPromotion { get; set; }
        public DateTime? DateFinPromotion { get; set; }
        public bool EstPack { get; set; } = false;
        public decimal? TauxTVA { get; set; }
        public string? ImagePath { get; set; }
        public string? Remarques { get; set; }
        
        // Navigation properties
        public Categorie? Categorie { get; set; }
        public SousCategorie? SousCategorie { get; set; }
        public Fournisseur? Fournisseur { get; set; }
        public List<CodeBarre> CodeBarres { get; set; } = new List<CodeBarre>();
        public List<ProduitPack> ProduitsEnPack { get; set; } = new List<ProduitPack>();
    }

    public class Categorie : BaseEntity
    {
        public string Nom { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool EstActif { get; set; } = true;
        public List<SousCategorie> SousCategories { get; set; } = new List<SousCategorie>();
    }

    public class SousCategorie : BaseEntity
    {
        public string Nom { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int CategorieId { get; set; }
        public bool EstActif { get; set; } = true;
        public Categorie? Categorie { get; set; }
    }

    public class CodeBarre : BaseEntity
    {
        public string Code { get; set; } = string.Empty;
        public int ProduitId { get; set; }
        public string? TypeCode { get; set; } // EAN13, Code128, etc.
        public bool EstPrincipal { get; set; } = false;
        public Produit? Produit { get; set; }
    }

    public class ProduitPack : BaseEntity
    {
        public int ProduitPackId { get; set; } // Le produit qui est un pack
        public int ProduitComposantId { get; set; } // Le produit composant du pack
        public decimal Quantite { get; set; }
        public decimal? PrixUnitaire { get; set; }
        
        public Produit? ProduitParent { get; set; }
        public Produit? ProduitComposant { get; set; }
    }
}

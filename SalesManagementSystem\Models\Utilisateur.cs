using System;

namespace SalesManagementSystem.Models
{
    public class Utilisateur : BaseEntity
    {
        public string NomUtilisateur { get; set; } = string.Empty;
        public string MotDePasse { get; set; } = string.Empty;
        public string Nom { get; set; } = string.Empty;
        public string Prenom { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Telephone { get; set; }
        public TypeUtilisateur TypeUtilisateur { get; set; }
        public bool EstActif { get; set; } = true;
        public DateTime? DerniereConnexion { get; set; }
        
        // Permissions
        public bool PeutVoir { get; set; } = true;
        public bool PeutAjouter { get; set; } = false;
        public bool PeutModifier { get; set; } = false;
        public bool PeutSupprimer { get; set; } = false;
        public bool PeutImprimer { get; set; } = false;
        public bool PeutExporter { get; set; } = false;
        public bool EstAdministrateur { get; set; } = false;
    }

    public enum TypeUtilisateur
    {
        Administrateur = 1,
        Gestionnaire = 2,
        Vendeur = 3,
        <PERSON><PERSON><PERSON>er = 4,
        Comptable = 5,
        Consultation = 6
    }
}

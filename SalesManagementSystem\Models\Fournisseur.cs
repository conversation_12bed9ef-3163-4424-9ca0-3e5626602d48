using System;
using System.Collections.Generic;

namespace SalesManagementSystem.Models
{
    public class Fournisseur : BaseEntity
    {
        public string CodeFournisseur { get; set; } = string.Empty;
        public string RaisonSociale { get; set; } = string.Empty;
        public string? Nom { get; set; }
        public string? Prenom { get; set; }
        
        // Informations de contact
        public string? Adresse { get; set; }
        public string? Ville { get; set; }
        public string? CodePostal { get; set; }
        public string? Pays { get; set; } = "France";
        public string? Telephone { get; set; }
        public string? Mobile { get; set; }
        public string? Fax { get; set; }
        public string? Email { get; set; }
        public string? SiteWeb { get; set; }
        
        // Informations fiscales
        public string? NumeroTVA { get; set; }
        public string? RegistreCommerce { get; set; }
        public string? CodeNAF { get; set; }
        
        // Informations commerciales
        public decimal SoldeInitial { get; set; }
        public decimal SoldeActuel { get; set; }
        public int DelaiPaiement { get; set; } = 30; // en jours
        public decimal RemiseHabituelle { get; set; }
        
        // Informations bancaires
        public string? Banque { get; set; }
        public string? IBAN { get; set; }
        public string? BIC { get; set; }
        public string? RIB { get; set; }
        
        // Statut
        public bool EstActif { get; set; } = true;
        public bool EstBloque { get; set; } = false;
        public string? RaisonBlocage { get; set; }
        public DateTime? DateDernierAchat { get; set; }
        
        // Contact commercial
        public string? NomContactCommercial { get; set; }
        public string? TelephoneContactCommercial { get; set; }
        public string? EmailContactCommercial { get; set; }
        
        // Remarques
        public string? Remarques { get; set; }
        
        // Navigation properties
        public List<Produit> Produits { get; set; } = new List<Produit>();
        public List<FactureAchat> FacturesAchat { get; set; } = new List<FactureAchat>();
        public List<CommandeAchat> CommandesAchat { get; set; } = new List<CommandeAchat>();
    }
}

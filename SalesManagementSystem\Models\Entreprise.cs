using System;

namespace SalesManagementSystem.Models
{
    public class Entreprise : BaseEntity
    {
        // Informations générales
        public string RaisonSociale { get; set; } = string.Empty;
        public string? NomCommercial { get; set; }
        public string? Slogan { get; set; }
        public string? FormeJuridique { get; set; }
        public string? Capital { get; set; }
        
        // Adresse
        public string? Adresse { get; set; }
        public string? Ville { get; set; }
        public string? CodePostal { get; set; }
        public string? Pays { get; set; } = "France";
        
        // Contact
        public string? Telephone { get; set; }
        public string? Mobile { get; set; }
        public string? Fax { get; set; }
        public string? Email { get; set; }
        public string? SiteWeb { get; set; }
        
        // Informations fiscales
        public string? NumeroTVA { get; set; }
        public string? RegistreCommerce { get; set; }
        public string? CodeNAF { get; set; }
        public string? SIRET { get; set; }
        public string? SIREN { get; set; }
        
        // Informations bancaires
        public string? Banque { get; set; }
        public string? IBAN { get; set; }
        public string? BIC { get; set; }
        public string? RIB { get; set; }
        
        // Logo et signature
        public string? CheminLogo { get; set; }
        public string? CheminSignature { get; set; }
        public string? CheminCachet { get; set; }
        
        // Paramètres de facturation
        public string PrefixeFacture { get; set; } = "FAC";
        public string PrefixeDevis { get; set; } = "DEV";
        public string PrefixeProforma { get; set; } = "PRO";
        public string PrefixeAvoir { get; set; } = "AVO";
        public string PrefixeFactureAchat { get; set; } = "ACH";
        
        public int NumeroFactureSuivant { get; set; } = 1;
        public int NumeroDevisSuivant { get; set; } = 1;
        public int NumeroProformaSuivant { get; set; } = 1;
        public int NumeroAvoirSuivant { get; set; } = 1;
        public int NumeroFactureAchatSuivant { get; set; } = 1;
        
        // Paramètres TVA et Timbre
        public decimal TauxTVADefaut { get; set; } = 20.0m;
        public decimal MontantTimbre { get; set; } = 0.0m;
        public bool AppliquerTimbreAutomatiquement { get; set; } = false;
        public decimal SeuilApplicationTimbre { get; set; } = 0.0m;
        
        // Paramètres de paiement
        public int DelaiPaiementDefaut { get; set; } = 30;
        public string ConditionsPaiementDefaut { get; set; } = "Paiement à 30 jours";
        
        // Mentions légales
        public string? MentionsLegales { get; set; }
        public string? ConditionsGeneralesVente { get; set; }
        
        // Paramètres d'impression
        public bool AfficherPrixHT { get; set; } = true;
        public bool AfficherPrixTTC { get; set; } = true;
        public bool AfficherTVA { get; set; } = true;
        public bool AfficherTimbre { get; set; } = true;
        public bool AfficherRemise { get; set; } = true;
        
        // Paramètres de sauvegarde
        public string? CheminSauvegarde { get; set; }
        public bool SauvegardeAutomatique { get; set; } = true;
        public int FrequenceSauvegarde { get; set; } = 24; // en heures
        
        // Paramètres de sécurité
        public bool ExigerMotDePasseComplexe { get; set; } = true;
        public int DureeValiditeMotDePasse { get; set; } = 90; // en jours
        public int NombreTentativesConnexionMax { get; set; } = 3;
        public int DureeBlocageCompte { get; set; } = 30; // en minutes
        
        // Paramètres d'affichage
        public string DeviseDefaut { get; set; } = "€";
        public string FormatDateDefaut { get; set; } = "dd/MM/yyyy";
        public string FormatNombreDefaut { get; set; } = "N2";
        public string CultureDefaut { get; set; } = "fr-FR";
        
        // Paramètres de stock
        public bool GestionStockActive { get; set; } = true;
        public bool AlerteStockMinimum { get; set; } = true;
        public bool AutorisationVenteStockNegatif { get; set; } = false;
        public bool CalculPMPAutomatique { get; set; } = true;
        
        // Paramètres de numérotation
        public bool ResetNumerotationAnnuelle { get; set; } = true;
        public int AnneeComptable { get; set; } = DateTime.Now.Year;
        
        // Dernière mise à jour
        public DateTime DerniereMiseAJour { get; set; } = DateTime.Now;
    }

    public class ParametreSysteme : BaseEntity
    {
        public string Cle { get; set; } = string.Empty;
        public string Valeur { get; set; } = string.Empty;
        public string? Description { get; set; }
        public TypeParametre TypeParametre { get; set; }
        public bool EstModifiable { get; set; } = true;
    }

    public enum TypeParametre
    {
        Texte = 1,
        Nombre = 2,
        Booleen = 3,
        Date = 4,
        Decimal = 5,
        Fichier = 6
    }
}

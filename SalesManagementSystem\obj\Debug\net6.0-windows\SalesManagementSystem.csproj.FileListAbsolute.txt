C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesManagementSystem.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesManagementSystem.Resources.Strings.resources
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesManagementSystem.csproj.GenerateResource.cache
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesManagementSystem.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesManagementSystem.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesManagementSystem.AssemblyInfo.cs
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesManagementSystem.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\SalesManagementSystem.exe
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\SalesManagementSystem.dll.config
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\SalesManagementSystem.deps.json
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\SalesManagementSystem.runtimeconfig.json
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\SalesManagementSystem.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\SalesManagementSystem.pdb
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\BCrypt.Net-Next.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\BouncyCastle.Crypto.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Dapper.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\EPPlus.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\EPPlus.Interfaces.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\EPPlus.System.Drawing.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\itextsharp.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Microsoft.Data.Sqlite.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Microsoft.IO.RecyclableMemoryStream.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Serilog.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\Serilog.Sinks.File.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\SQLitePCLRaw.batteries_v2.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\SQLitePCLRaw.core.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\browser-wasm\nativeassets\net6.0\e_sqlite3.a
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-musl-arm\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-musl-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-ppc64le\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\win-arm\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\win-x64\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\win-x86\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesMan.8E186F91.Up2Date
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesManagementSystem.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\refint\SalesManagementSystem.dll
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesManagementSystem.pdb
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\SalesManagementSystem.genruntimeconfig.cache
C:\Users\<USER>\Desktop\EE\SalesManagementSystem\obj\Debug\net6.0-windows\ref\SalesManagementSystem.dll

using Dapper;
using SalesManagementSystem.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace SalesManagementSystem.Data
{
    public class UtilisateurRepository : IUtilisateurRepository
    {
        private readonly DatabaseContext _context;

        public UtilisateurRepository(DatabaseContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Utilisateur>> GetAllAsync()
        {
            using var connection = _context.GetConnection();
            return await connection.QueryAsync<Utilisateur>(
                "SELECT * FROM Utilisateurs WHERE EstSupprime = 0 ORDER BY Nom, Prenom");
        }

        public async Task<Utilisateur?> GetByIdAsync(int id)
        {
            using var connection = _context.GetConnection();
            return await connection.QueryFirstOrDefaultAsync<Utilisateur>(
                "SELECT * FROM Utilisateurs WHERE Id = @Id AND EstSupprime = 0", new { Id = id });
        }

        public async Task<int> AddAsync(Utilisateur entity)
        {
            using var connection = _context.GetConnection();
            var sql = @"
                INSERT INTO Utilisateurs (
                    NomUtilisateur, MotDePasse, Nom, Prenom, Email, Telephone, TypeUtilisateur,
                    EstActif, PeutVoir, PeutAjouter, PeutModifier, PeutSupprimer, PeutImprimer,
                    PeutExporter, EstAdministrateur, DateCreation, UtilisateurCreation
                ) VALUES (
                    @NomUtilisateur, @MotDePasse, @Nom, @Prenom, @Email, @Telephone, @TypeUtilisateur,
                    @EstActif, @PeutVoir, @PeutAjouter, @PeutModifier, @PeutSupprimer, @PeutImprimer,
                    @PeutExporter, @EstAdministrateur, @DateCreation, @UtilisateurCreation
                );
                SELECT last_insert_rowid();";

            entity.DateCreation = DateTime.Now;
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public async Task<bool> UpdateAsync(Utilisateur entity)
        {
            using var connection = _context.GetConnection();
            var sql = @"
                UPDATE Utilisateurs SET
                    NomUtilisateur = @NomUtilisateur,
                    Nom = @Nom,
                    Prenom = @Prenom,
                    Email = @Email,
                    Telephone = @Telephone,
                    TypeUtilisateur = @TypeUtilisateur,
                    EstActif = @EstActif,
                    PeutVoir = @PeutVoir,
                    PeutAjouter = @PeutAjouter,
                    PeutModifier = @PeutModifier,
                    PeutSupprimer = @PeutSupprimer,
                    PeutImprimer = @PeutImprimer,
                    PeutExporter = @PeutExporter,
                    EstAdministrateur = @EstAdministrateur,
                    DateModification = @DateModification,
                    UtilisateurModification = @UtilisateurModification
                WHERE Id = @Id";

            entity.DateModification = DateTime.Now;
            var rowsAffected = await connection.ExecuteAsync(sql, entity);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = _context.GetConnection();
            var rowsAffected = await connection.ExecuteAsync(
                "DELETE FROM Utilisateurs WHERE Id = @Id", new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> SoftDeleteAsync(int id)
        {
            using var connection = _context.GetConnection();
            var rowsAffected = await connection.ExecuteAsync(
                "UPDATE Utilisateurs SET EstSupprime = 1, DateModification = @DateModification WHERE Id = @Id",
                new { Id = id, DateModification = DateTime.Now });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<Utilisateur>> SearchAsync(string searchTerm)
        {
            using var connection = _context.GetConnection();
            var sql = @"
                SELECT * FROM Utilisateurs 
                WHERE EstSupprime = 0 
                AND (Nom LIKE @SearchTerm OR Prenom LIKE @SearchTerm OR NomUtilisateur LIKE @SearchTerm OR Email LIKE @SearchTerm)
                ORDER BY Nom, Prenom";
            
            return await connection.QueryAsync<Utilisateur>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<int> CountAsync()
        {
            using var connection = _context.GetConnection();
            return await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Utilisateurs WHERE EstSupprime = 0");
        }

        public async Task<bool> ExistsAsync(int id)
        {
            using var connection = _context.GetConnection();
            var count = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Utilisateurs WHERE Id = @Id AND EstSupprime = 0", new { Id = id });
            return count > 0;
        }

        public async Task<Utilisateur?> GetByNomUtilisateurAsync(string nomUtilisateur)
        {
            using var connection = _context.GetConnection();
            return await connection.QueryFirstOrDefaultAsync<Utilisateur>(
                "SELECT * FROM Utilisateurs WHERE NomUtilisateur = @NomUtilisateur AND EstSupprime = 0 AND EstActif = 1",
                new { NomUtilisateur = nomUtilisateur });
        }

        public async Task<bool> ValidateCredentialsAsync(string nomUtilisateur, string motDePasse)
        {
            var utilisateur = await GetByNomUtilisateurAsync(nomUtilisateur);
            if (utilisateur == null) return false;

            return BCrypt.Net.BCrypt.Verify(motDePasse, utilisateur.MotDePasse);
        }

        public async Task<bool> UpdateLastLoginAsync(int utilisateurId)
        {
            using var connection = _context.GetConnection();
            var rowsAffected = await connection.ExecuteAsync(
                "UPDATE Utilisateurs SET DerniereConnexion = @DerniereConnexion WHERE Id = @Id",
                new { Id = utilisateurId, DerniereConnexion = DateTime.Now });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<Utilisateur>> GetActiveUsersAsync()
        {
            using var connection = _context.GetConnection();
            return await connection.QueryAsync<Utilisateur>(
                "SELECT * FROM Utilisateurs WHERE EstSupprime = 0 AND EstActif = 1 ORDER BY Nom, Prenom");
        }

        public async Task<bool> ChangePasswordAsync(int utilisateurId, string nouveauMotDePasse)
        {
            using var connection = _context.GetConnection();
            var hashedPassword = BCrypt.Net.BCrypt.HashPassword(nouveauMotDePasse);
            var rowsAffected = await connection.ExecuteAsync(
                "UPDATE Utilisateurs SET MotDePasse = @MotDePasse, DateModification = @DateModification WHERE Id = @Id",
                new { Id = utilisateurId, MotDePasse = hashedPassword, DateModification = DateTime.Now });
            return rowsAffected > 0;
        }
    }
}

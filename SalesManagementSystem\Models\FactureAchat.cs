using System;
using System.Collections.Generic;

namespace SalesManagementSystem.Models
{
    public class FactureAchat : BaseEntity
    {
        public string NumeroFacture { get; set; } = string.Empty;
        public string? NumeroFactureFournisseur { get; set; }
        public DateTime DateFacture { get; set; } = DateTime.Now;
        public DateTime DateEcheance { get; set; }
        public int FournisseurId { get; set; }
        public TypeFactureAchat TypeFacture { get; set; }
        public StatutFactureAchat Statut { get; set; } = StatutFactureAchat.Brouillon;
        
        // Montants
        public decimal MontantHT { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal MontantRemise { get; set; }
        public decimal PourcentageRemise { get; set; }
        public decimal MontantTimbre { get; set; }
        public decimal MontantPaye { get; set; }
        public decimal MontantRestant { get; set; }
        
        // Informations complémentaires
        public string? Remarques { get; set; }
        public string? ConditionsPaiement { get; set; }
        public string? ModePaiement { get; set; }
        public bool EstPayee { get; set; } = false;
        public DateTime? DatePaiement { get; set; }
        public DateTime? DateLivraison { get; set; }
        
        // Référence commande
        public int? CommandeAchatId { get; set; }
        
        // Navigation properties
        public Fournisseur? Fournisseur { get; set; }
        public List<LigneFactureAchat> Lignes { get; set; } = new List<LigneFactureAchat>();
        public List<PaiementFactureAchat> Paiements { get; set; } = new List<PaiementFactureAchat>();
        public CommandeAchat? CommandeAchat { get; set; }
    }

    public class LigneFactureAchat : BaseEntity
    {
        public int FactureAchatId { get; set; }
        public int ProduitId { get; set; }
        public string DesignationProduit { get; set; } = string.Empty;
        public decimal Quantite { get; set; }
        public string Unite { get; set; } = string.Empty;
        public decimal PrixUnitaire { get; set; }
        public decimal PrixUnitaireHT { get; set; }
        public decimal MontantHT { get; set; }
        public decimal TauxTVA { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal PourcentageRemise { get; set; }
        public decimal MontantRemise { get; set; }
        
        // Navigation properties
        public FactureAchat? FactureAchat { get; set; }
        public Produit? Produit { get; set; }
    }

    public class PaiementFactureAchat : BaseEntity
    {
        public int FactureAchatId { get; set; }
        public DateTime DatePaiement { get; set; } = DateTime.Now;
        public decimal Montant { get; set; }
        public ModePaiement ModePaiement { get; set; }
        public string? Reference { get; set; }
        public string? Remarques { get; set; }
        
        // Navigation properties
        public FactureAchat? FactureAchat { get; set; }
    }

    public enum TypeFactureAchat
    {
        Achat = 1,
        RetourAchat = 2,
        Avoir = 3
    }

    public enum StatutFactureAchat
    {
        Brouillon = 1,
        Validee = 2,
        Recue = 3,
        Payee = 4,
        Annulee = 5,
        Retournee = 6
    }
}

using System;

namespace SalesManagementSystem.Models
{
    public class MouvementStock : BaseEntity
    {
        public int ProduitId { get; set; }
        public TypeMouvement TypeMouvement { get; set; }
        public decimal Quantite { get; set; }
        public decimal PrixUnitaire { get; set; }
        public decimal StockAvant { get; set; }
        public decimal StockApres { get; set; }
        public string? Reference { get; set; } // Numéro facture, bon de livraison, etc.
        public string? Motif { get; set; }
        public DateTime DateMouvement { get; set; } = DateTime.Now;
        public string? Remarques { get; set; }
        
        // Références aux documents
        public int? FactureId { get; set; }
        public int? FactureAchatId { get; set; }
        public int? InventaireId { get; set; }
        
        // Navigation properties
        public Produit? Produit { get; set; }
        public Facture? Facture { get; set; }
        public FactureAchat? FactureAchat { get; set; }
        public Inventaire? Inventaire { get; set; }
    }

    public class Inventaire : BaseEntity
    {
        public string NumeroInventaire { get; set; } = string.Empty;
        public DateTime DateInventaire { get; set; } = DateTime.Now;
        public string? Description { get; set; }
        public StatutInventaire Statut { get; set; } = StatutInventaire.EnCours;
        public string? Remarques { get; set; }
        public decimal TotalEcartValeur { get; set; }
        public int NombreProduits { get; set; }
        
        // Navigation properties
        public List<LigneInventaire> Lignes { get; set; } = new List<LigneInventaire>();
    }

    public class LigneInventaire : BaseEntity
    {
        public int InventaireId { get; set; }
        public int ProduitId { get; set; }
        public decimal StockTheorique { get; set; }
        public decimal StockPhysique { get; set; }
        public decimal Ecart { get; set; }
        public decimal PrixUnitaire { get; set; }
        public decimal ValeurEcart { get; set; }
        public string? Remarques { get; set; }
        
        // Navigation properties
        public Inventaire? Inventaire { get; set; }
        public Produit? Produit { get; set; }
    }

    public class CommandeAchat : BaseEntity
    {
        public string NumeroCommande { get; set; } = string.Empty;
        public DateTime DateCommande { get; set; } = DateTime.Now;
        public DateTime DateLivraisonPrevue { get; set; }
        public int FournisseurId { get; set; }
        public StatutCommande Statut { get; set; } = StatutCommande.Brouillon;
        
        // Montants
        public decimal MontantHT { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal MontantRemise { get; set; }
        public decimal PourcentageRemise { get; set; }
        
        // Informations complémentaires
        public string? Remarques { get; set; }
        public string? ConditionsLivraison { get; set; }
        public string? LieuLivraison { get; set; }
        
        // Conversion
        public bool EstConvertiEnFacture { get; set; } = false;
        public DateTime? DateConversion { get; set; }
        
        // Navigation properties
        public Fournisseur? Fournisseur { get; set; }
        public List<LigneCommandeAchat> Lignes { get; set; } = new List<LigneCommandeAchat>();
        public List<FactureAchat> FacturesAchat { get; set; } = new List<FactureAchat>();
    }

    public class LigneCommandeAchat : BaseEntity
    {
        public int CommandeAchatId { get; set; }
        public int ProduitId { get; set; }
        public string DesignationProduit { get; set; } = string.Empty;
        public decimal QuantiteCommandee { get; set; }
        public decimal QuantiteLivree { get; set; }
        public decimal QuantiteRestante { get; set; }
        public string Unite { get; set; } = string.Empty;
        public decimal PrixUnitaire { get; set; }
        public decimal PrixUnitaireHT { get; set; }
        public decimal MontantHT { get; set; }
        public decimal TauxTVA { get; set; }
        public decimal MontantTVA { get; set; }
        public decimal MontantTTC { get; set; }
        public decimal PourcentageRemise { get; set; }
        public decimal MontantRemise { get; set; }
        
        // Navigation properties
        public CommandeAchat? CommandeAchat { get; set; }
        public Produit? Produit { get; set; }
    }

    public enum TypeMouvement
    {
        Entree = 1,
        Sortie = 2,
        Ajustement = 3,
        Transfert = 4,
        Inventaire = 5,
        Retour = 6,
        Perte = 7,
        Production = 8
    }

    public enum StatutInventaire
    {
        EnCours = 1,
        Termine = 2,
        Valide = 3,
        Annule = 4
    }

    public enum StatutCommande
    {
        Brouillon = 1,
        Envoyee = 2,
        Confirmee = 3,
        PartielLivree = 4,
        Livree = 5,
        Facturee = 6,
        Annulee = 7
    }
}

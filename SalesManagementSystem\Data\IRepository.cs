using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Data
{
    public interface IRepository<T> where T : BaseEntity
    {
        Task<IEnumerable<T>> GetAllAsync();
        Task<T?> GetByIdAsync(int id);
        Task<int> AddAsync(T entity);
        Task<bool> UpdateAsync(T entity);
        Task<bool> DeleteAsync(int id);
        Task<bool> SoftDeleteAsync(int id);
        Task<IEnumerable<T>> SearchAsync(string searchTerm);
        Task<int> CountAsync();
        Task<bool> ExistsAsync(int id);
    }

    public interface IUtilisateurRepository : IRepository<Utilisateur>
    {
        Task<Utilisateur?> GetByNomUtilisateurAsync(string nomUtilisateur);
        Task<bool> ValidateCredentialsAsync(string nomUtilisateur, string motDePasse);
        Task<bool> UpdateLastLoginAsync(int utilisateurId);
        Task<IEnumerable<Utilisateur>> GetActiveUsersAsync();
        Task<bool> ChangePasswordAsync(int utilisateurId, string nouveauMotDePasse);
    }

    public interface IProduitRepository : IRepository<Produit>
    {
        Task<IEnumerable<Produit>> GetByCategorieAsync(int categorieId);
        Task<IEnumerable<Produit>> GetByFournisseurAsync(int fournisseurId);
        Task<Produit?> GetByCodeBarreAsync(string codeBarre);
        Task<Produit?> GetByCodeProduitAsync(string codeProduit);
        Task<IEnumerable<Produit>> GetProduitsEnStockAsync();
        Task<IEnumerable<Produit>> GetProduitsStockMinimumAsync();
        Task<IEnumerable<Produit>> GetProduitsEnPromotionAsync();
        Task<bool> UpdateStockAsync(int produitId, decimal nouvelleQuantite);
        Task<bool> AjusterStockAsync(int produitId, decimal quantiteAjustement);
    }

    public interface IClientRepository : IRepository<Client>
    {
        Task<Client?> GetByCodeClientAsync(string codeClient);
        Task<IEnumerable<Client>> GetClientsActifsAsync();
        Task<IEnumerable<Client>> GetClientsDebiteurs();
        Task<decimal> GetSoldeClientAsync(int clientId);
        Task<bool> UpdateSoldeAsync(int clientId, decimal nouveauSolde);
        Task<IEnumerable<Client>> GetClientsByTypeAsync(TypeClient typeClient);
    }

    public interface IFournisseurRepository : IRepository<Fournisseur>
    {
        Task<Fournisseur?> GetByCodeFournisseurAsync(string codeFournisseur);
        Task<IEnumerable<Fournisseur>> GetFournisseursActifsAsync();
        Task<IEnumerable<Fournisseur>> GetFournisseursCrediteurs();
        Task<decimal> GetSoldeFournisseurAsync(int fournisseurId);
        Task<bool> UpdateSoldeAsync(int fournisseurId, decimal nouveauSolde);
    }

    public interface IFactureRepository : IRepository<Facture>
    {
        Task<string> GetNextNumeroFactureAsync();
        Task<IEnumerable<Facture>> GetFacturesByClientAsync(int clientId);
        Task<IEnumerable<Facture>> GetFacturesByPeriodeAsync(DateTime dateDebut, DateTime dateFin);
        Task<IEnumerable<Facture>> GetFacturesNonPayeesAsync();
        Task<IEnumerable<Facture>> GetFacturesEchuesAsync();
        Task<decimal> GetChiffresAffairesPeriodeAsync(DateTime dateDebut, DateTime dateFin);
        Task<bool> MarquerCommePayeeAsync(int factureId);
        Task<bool> AnnulerFactureAsync(int factureId);
    }

    public interface IDevisRepository : IRepository<Devis>
    {
        Task<string> GetNextNumeroDevisAsync();
        Task<IEnumerable<Devis>> GetDevisByClientAsync(int clientId);
        Task<IEnumerable<Devis>> GetDevisByPeriodeAsync(DateTime dateDebut, DateTime dateFin);
        Task<IEnumerable<Devis>> GetDevisEnAttenteAsync();
        Task<IEnumerable<Devis>> GetDevisExpiresAsync();
        Task<bool> ConvertirEnFactureAsync(int devisId, int factureId);
        Task<bool> AccepterDevisAsync(int devisId);
        Task<bool> RefuserDevisAsync(int devisId);
    }

    public interface IStockRepository
    {
        Task<IEnumerable<MouvementStock>> GetMouvementsAsync(int? produitId = null, DateTime? dateDebut = null, DateTime? dateFin = null);
        Task<bool> AjouterMouvementAsync(MouvementStock mouvement);
        Task<decimal> GetStockActuelAsync(int produitId);
        Task<IEnumerable<Produit>> GetProduitsStockFaibleAsync();
        Task<bool> EffectuerInventaireAsync(int inventaireId);
    }

    public interface IComptabiliteRepository
    {
        Task<IEnumerable<CompteComptable>> GetComptesAsync();
        Task<IEnumerable<EcritureComptable>> GetEcrituresAsync(DateTime? dateDebut = null, DateTime? dateFin = null);
        Task<bool> AjouterEcritureAsync(EcritureComptable ecriture);
        Task<bool> ValiderEcritureAsync(int ecritureId);
        Task<decimal> GetSoldeCompteAsync(int compteId, DateTime? dateFinPeriode = null);
        Task<Dictionary<string, decimal>> GetBilanAsync(DateTime dateBilan);
        Task<Dictionary<string, decimal>> GetCompteResultatAsync(DateTime dateDebut, DateTime dateFin);
    }

    public interface IEntrepriseRepository : IRepository<Entreprise>
    {
        Task<Entreprise?> GetEntrepriseAsync();
        Task<bool> UpdateParametresAsync(Entreprise entreprise);
        Task<string> GetParametreAsync(string cle);
        Task<bool> SetParametreAsync(string cle, string valeur);
    }
}

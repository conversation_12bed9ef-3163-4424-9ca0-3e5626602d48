using Microsoft.Data.Sqlite;
using System;
using System.Data;
using System.IO;
using Dapper;

namespace SalesManagementSystem.Data
{
    public class DatabaseContext : IDisposable
    {
        private readonly string _connectionString;
        private SqliteConnection? _connection;

        public DatabaseContext()
        {
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "SalesManagement.db");
            var directory = Path.GetDirectoryName(dbPath);
            
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            _connectionString = $"Data Source={dbPath};";
        }

        public IDbConnection GetConnection()
        {
            if (_connection == null || _connection.State != ConnectionState.Open)
            {
                _connection = new SqliteConnection(_connectionString);
                _connection.Open();
            }
            return _connection;
        }

        public async Task InitializeDatabaseAsync()
        {
            using var connection = GetConnection();
            
            // Créer les tables
            await CreateTablesAsync(connection);
            
            // Insérer les données par défaut
            await InsertDefaultDataAsync(connection);
        }

        private async Task CreateTablesAsync(IDbConnection connection)
        {
            var createTablesSql = @"
                -- Table Utilisateurs
                CREATE TABLE IF NOT EXISTS Utilisateurs (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    NomUtilisateur TEXT NOT NULL UNIQUE,
                    MotDePasse TEXT NOT NULL,
                    Nom TEXT NOT NULL,
                    Prenom TEXT NOT NULL,
                    Email TEXT NOT NULL,
                    Telephone TEXT,
                    TypeUtilisateur INTEGER NOT NULL,
                    EstActif INTEGER NOT NULL DEFAULT 1,
                    DerniereConnexion TEXT,
                    PeutVoir INTEGER NOT NULL DEFAULT 1,
                    PeutAjouter INTEGER NOT NULL DEFAULT 0,
                    PeutModifier INTEGER NOT NULL DEFAULT 0,
                    PeutSupprimer INTEGER NOT NULL DEFAULT 0,
                    PeutImprimer INTEGER NOT NULL DEFAULT 0,
                    PeutExporter INTEGER NOT NULL DEFAULT 0,
                    EstAdministrateur INTEGER NOT NULL DEFAULT 0,
                    DateCreation TEXT NOT NULL,
                    DateModification TEXT,
                    UtilisateurCreation TEXT,
                    UtilisateurModification TEXT,
                    EstSupprime INTEGER NOT NULL DEFAULT 0
                );

                -- Table Entreprise
                CREATE TABLE IF NOT EXISTS Entreprise (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RaisonSociale TEXT NOT NULL,
                    NomCommercial TEXT,
                    Slogan TEXT,
                    FormeJuridique TEXT,
                    Capital TEXT,
                    Adresse TEXT,
                    Ville TEXT,
                    CodePostal TEXT,
                    Pays TEXT DEFAULT 'France',
                    Telephone TEXT,
                    Mobile TEXT,
                    Fax TEXT,
                    Email TEXT,
                    SiteWeb TEXT,
                    NumeroTVA TEXT,
                    RegistreCommerce TEXT,
                    CodeNAF TEXT,
                    SIRET TEXT,
                    SIREN TEXT,
                    Banque TEXT,
                    IBAN TEXT,
                    BIC TEXT,
                    RIB TEXT,
                    CheminLogo TEXT,
                    CheminSignature TEXT,
                    CheminCachet TEXT,
                    PrefixeFacture TEXT DEFAULT 'FAC',
                    PrefixeDevis TEXT DEFAULT 'DEV',
                    PrefixeProforma TEXT DEFAULT 'PRO',
                    PrefixeAvoir TEXT DEFAULT 'AVO',
                    PrefixeFactureAchat TEXT DEFAULT 'ACH',
                    NumeroFactureSuivant INTEGER DEFAULT 1,
                    NumeroDevisSuivant INTEGER DEFAULT 1,
                    NumeroProformaSuivant INTEGER DEFAULT 1,
                    NumeroAvoirSuivant INTEGER DEFAULT 1,
                    NumeroFactureAchatSuivant INTEGER DEFAULT 1,
                    TauxTVADefaut REAL DEFAULT 20.0,
                    MontantTimbre REAL DEFAULT 0.0,
                    AppliquerTimbreAutomatiquement INTEGER DEFAULT 0,
                    SeuilApplicationTimbre REAL DEFAULT 0.0,
                    DelaiPaiementDefaut INTEGER DEFAULT 30,
                    ConditionsPaiementDefaut TEXT DEFAULT 'Paiement à 30 jours',
                    MentionsLegales TEXT,
                    ConditionsGeneralesVente TEXT,
                    AfficherPrixHT INTEGER DEFAULT 1,
                    AfficherPrixTTC INTEGER DEFAULT 1,
                    AfficherTVA INTEGER DEFAULT 1,
                    AfficherTimbre INTEGER DEFAULT 1,
                    AfficherRemise INTEGER DEFAULT 1,
                    CheminSauvegarde TEXT,
                    SauvegardeAutomatique INTEGER DEFAULT 1,
                    FrequenceSauvegarde INTEGER DEFAULT 24,
                    ExigerMotDePasseComplexe INTEGER DEFAULT 1,
                    DureeValiditeMotDePasse INTEGER DEFAULT 90,
                    NombreTentativesConnexionMax INTEGER DEFAULT 3,
                    DureeBlocageCompte INTEGER DEFAULT 30,
                    DeviseDefaut TEXT DEFAULT '€',
                    FormatDateDefaut TEXT DEFAULT 'dd/MM/yyyy',
                    FormatNombreDefaut TEXT DEFAULT 'N2',
                    CultureDefaut TEXT DEFAULT 'fr-FR',
                    GestionStockActive INTEGER DEFAULT 1,
                    AlerteStockMinimum INTEGER DEFAULT 1,
                    AutorisationVenteStockNegatif INTEGER DEFAULT 0,
                    CalculPMPAutomatique INTEGER DEFAULT 1,
                    ResetNumerotationAnnuelle INTEGER DEFAULT 1,
                    AnneeComptable INTEGER DEFAULT 2024,
                    DerniereMiseAJour TEXT NOT NULL,
                    DateCreation TEXT NOT NULL,
                    DateModification TEXT,
                    UtilisateurCreation TEXT,
                    UtilisateurModification TEXT,
                    EstSupprime INTEGER NOT NULL DEFAULT 0
                );

                -- Table Categories
                CREATE TABLE IF NOT EXISTS Categories (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Nom TEXT NOT NULL,
                    Description TEXT,
                    EstActif INTEGER NOT NULL DEFAULT 1,
                    DateCreation TEXT NOT NULL,
                    DateModification TEXT,
                    UtilisateurCreation TEXT,
                    UtilisateurModification TEXT,
                    EstSupprime INTEGER NOT NULL DEFAULT 0
                );

                -- Table SousCategories
                CREATE TABLE IF NOT EXISTS SousCategories (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Nom TEXT NOT NULL,
                    Description TEXT,
                    CategorieId INTEGER NOT NULL,
                    EstActif INTEGER NOT NULL DEFAULT 1,
                    DateCreation TEXT NOT NULL,
                    DateModification TEXT,
                    UtilisateurCreation TEXT,
                    UtilisateurModification TEXT,
                    EstSupprime INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY (CategorieId) REFERENCES Categories(Id)
                );

                -- Table Fournisseurs
                CREATE TABLE IF NOT EXISTS Fournisseurs (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CodeFournisseur TEXT NOT NULL UNIQUE,
                    RaisonSociale TEXT NOT NULL,
                    Nom TEXT,
                    Prenom TEXT,
                    Adresse TEXT,
                    Ville TEXT,
                    CodePostal TEXT,
                    Pays TEXT DEFAULT 'France',
                    Telephone TEXT,
                    Mobile TEXT,
                    Fax TEXT,
                    Email TEXT,
                    SiteWeb TEXT,
                    NumeroTVA TEXT,
                    RegistreCommerce TEXT,
                    CodeNAF TEXT,
                    SoldeInitial REAL DEFAULT 0,
                    SoldeActuel REAL DEFAULT 0,
                    DelaiPaiement INTEGER DEFAULT 30,
                    RemiseHabituelle REAL DEFAULT 0,
                    Banque TEXT,
                    IBAN TEXT,
                    BIC TEXT,
                    RIB TEXT,
                    EstActif INTEGER NOT NULL DEFAULT 1,
                    EstBloque INTEGER NOT NULL DEFAULT 0,
                    RaisonBlocage TEXT,
                    DateDernierAchat TEXT,
                    NomContactCommercial TEXT,
                    TelephoneContactCommercial TEXT,
                    EmailContactCommercial TEXT,
                    Remarques TEXT,
                    DateCreation TEXT NOT NULL,
                    DateModification TEXT,
                    UtilisateurCreation TEXT,
                    UtilisateurModification TEXT,
                    EstSupprime INTEGER NOT NULL DEFAULT 0
                );

                -- Table Clients
                CREATE TABLE IF NOT EXISTS Clients (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CodeClient TEXT NOT NULL UNIQUE,
                    RaisonSociale TEXT NOT NULL,
                    Nom TEXT,
                    Prenom TEXT,
                    TypeClient INTEGER NOT NULL,
                    Adresse TEXT,
                    Ville TEXT,
                    CodePostal TEXT,
                    Pays TEXT DEFAULT 'France',
                    Telephone TEXT,
                    Mobile TEXT,
                    Fax TEXT,
                    Email TEXT,
                    SiteWeb TEXT,
                    NumeroTVA TEXT,
                    RegistreCommerce TEXT,
                    CodeNAF TEXT,
                    SoldeInitial REAL DEFAULT 0,
                    SoldeActuel REAL DEFAULT 0,
                    LimiteCredit REAL DEFAULT 0,
                    DelaiPaiement INTEGER DEFAULT 30,
                    RemiseHabituelle REAL DEFAULT 0,
                    TypePrixPreferentiel INTEGER DEFAULT 1,
                    EstActif INTEGER NOT NULL DEFAULT 1,
                    EstBloque INTEGER NOT NULL DEFAULT 0,
                    RaisonBlocage TEXT,
                    DateDernierAchat TEXT,
                    Remarques TEXT,
                    DateCreation TEXT NOT NULL,
                    DateModification TEXT,
                    UtilisateurCreation TEXT,
                    UtilisateurModification TEXT,
                    EstSupprime INTEGER NOT NULL DEFAULT 0
                );";

            await connection.ExecuteAsync(createTablesSql);

            // Créer les tables supplémentaires
            await CreateAdditionalTablesAsync(connection);
        }

        private async Task CreateAdditionalTablesAsync(IDbConnection connection)
        {
            var additionalTablesSql = @"
                -- Table Produits
                CREATE TABLE IF NOT EXISTS Produits (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CodeProduit TEXT NOT NULL UNIQUE,
                    Designation TEXT NOT NULL,
                    Description TEXT,
                    CategorieId INTEGER NOT NULL,
                    SousCategorieId INTEGER,
                    FournisseurId INTEGER NOT NULL,
                    PrixAchat REAL DEFAULT 0,
                    PrixVente REAL DEFAULT 0,
                    PrixGros REAL DEFAULT 0,
                    PrixDemiGros REAL DEFAULT 0,
                    PrixHT REAL DEFAULT 0,
                    PMP REAL DEFAULT 0,
                    StockInitial REAL DEFAULT 0,
                    StockActuel REAL DEFAULT 0,
                    StockMinimum REAL DEFAULT 0,
                    StockMaximum REAL DEFAULT 0,
                    StockReserve REAL DEFAULT 0,
                    UniteBase TEXT DEFAULT 'Pièce',
                    UniteGros TEXT,
                    FacteurConversion REAL,
                    EstActif INTEGER NOT NULL DEFAULT 1,
                    EstEnPromotion INTEGER NOT NULL DEFAULT 0,
                    PrixPromotion REAL,
                    DateDebutPromotion TEXT,
                    DateFinPromotion TEXT,
                    EstPack INTEGER NOT NULL DEFAULT 0,
                    TauxTVA REAL,
                    ImagePath TEXT,
                    Remarques TEXT,
                    DateCreation TEXT NOT NULL,
                    DateModification TEXT,
                    UtilisateurCreation TEXT,
                    UtilisateurModification TEXT,
                    EstSupprime INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY (CategorieId) REFERENCES Categories(Id),
                    FOREIGN KEY (SousCategorieId) REFERENCES SousCategories(Id),
                    FOREIGN KEY (FournisseurId) REFERENCES Fournisseurs(Id)
                );

                -- Table CodeBarres
                CREATE TABLE IF NOT EXISTS CodeBarres (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Code TEXT NOT NULL,
                    ProduitId INTEGER NOT NULL,
                    TypeCode TEXT,
                    EstPrincipal INTEGER NOT NULL DEFAULT 0,
                    DateCreation TEXT NOT NULL,
                    DateModification TEXT,
                    UtilisateurCreation TEXT,
                    UtilisateurModification TEXT,
                    EstSupprime INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY (ProduitId) REFERENCES Produits(Id)
                );

                -- Table ProduitsPack
                CREATE TABLE IF NOT EXISTS ProduitsPack (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProduitPackId INTEGER NOT NULL,
                    ProduitComposantId INTEGER NOT NULL,
                    Quantite REAL NOT NULL,
                    PrixUnitaire REAL,
                    DateCreation TEXT NOT NULL,
                    DateModification TEXT,
                    UtilisateurCreation TEXT,
                    UtilisateurModification TEXT,
                    EstSupprime INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY (ProduitPackId) REFERENCES Produits(Id),
                    FOREIGN KEY (ProduitComposantId) REFERENCES Produits(Id)
                );";

            await connection.ExecuteAsync(additionalTablesSql);
        }

        private async Task InsertDefaultDataAsync(IDbConnection connection)
        {
            // Vérifier si l'utilisateur admin existe déjà
            var adminExists = await connection.QueryFirstOrDefaultAsync<int>(
                "SELECT COUNT(*) FROM Utilisateurs WHERE NomUtilisateur = 'admin'");

            if (adminExists == 0)
            {
                // Créer l'utilisateur administrateur par défaut
                var hashedPassword = BCrypt.Net.BCrypt.HashPassword("admin123");
                await connection.ExecuteAsync(@"
                    INSERT INTO Utilisateurs (
                        NomUtilisateur, MotDePasse, Nom, Prenom, Email, TypeUtilisateur,
                        EstActif, PeutVoir, PeutAjouter, PeutModifier, PeutSupprimer,
                        PeutImprimer, PeutExporter, EstAdministrateur, DateCreation
                    ) VALUES (
                        'admin', @MotDePasse, 'Administrateur', 'Système', '<EMAIL>', 1,
                        1, 1, 1, 1, 1, 1, 1, 1, @DateCreation
                    )", new { MotDePasse = hashedPassword, DateCreation = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
            }

            // Vérifier si les données de l'entreprise existent
            var entrepriseExists = await connection.QueryFirstOrDefaultAsync<int>(
                "SELECT COUNT(*) FROM Entreprise");

            if (entrepriseExists == 0)
            {
                // Créer les données par défaut de l'entreprise
                await connection.ExecuteAsync(@"
                    INSERT INTO Entreprise (
                        RaisonSociale, DateCreation, DerniereMiseAJour
                    ) VALUES (
                        'Mon Entreprise', @DateCreation, @DerniereMiseAJour
                    )", new { 
                        DateCreation = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        DerniereMiseAJour = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    });
            }

            // Insérer les catégories par défaut
            var categoriesExist = await connection.QueryFirstOrDefaultAsync<int>(
                "SELECT COUNT(*) FROM Categories");

            if (categoriesExist == 0)
            {
                await connection.ExecuteAsync(@"
                    INSERT INTO Categories (Nom, Description, DateCreation) VALUES
                    ('Produits Généraux', 'Catégorie par défaut', @DateCreation),
                    ('Services', 'Services et prestations', @DateCreation),
                    ('Matières Premières', 'Matières premières et composants', @DateCreation)
                ", new { DateCreation = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
            }
        }

        public void Dispose()
        {
            _connection?.Dispose();
        }
    }
}

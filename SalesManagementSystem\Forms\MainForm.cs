using SalesManagementSystem.Services;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace SalesManagementSystem.Forms
{
    public partial class MainForm : Form
    {
        private MenuStrip menuPrincipal;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel lblUtilisateur;
        private ToolStripStatusLabel lblDate;
        private ToolStripStatusLabel lblHeure;
        private System.Windows.Forms.Timer timerHeure;
        private Panel panelPrincipal;

        public MainForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            InitializeMenu();
            InitializeStatusBar();
            InitializeTimer();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Text = "Système de Gestion des Ventes et Stocks - EE Solutions";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Icon = SystemIcons.Application;
            this.IsMdiContainer = true;
            
            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            // Panel principal
            panelPrincipal = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(236, 240, 241),
                BackgroundImage = CreateWelcomeBackground()
            };
            
            this.Controls.Add(panelPrincipal);
        }

        private void InitializeMenu()
        {
            menuPrincipal = new MenuStrip
            {
                BackColor = Color.FromArgb(52, 73, 94),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F)
            };

            // Menu Fichier
            var menuFichier = new ToolStripMenuItem("&Fichier");
            menuFichier.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("&Nouvelle Facture", null, (s, e) => OuvrirNouvelleFacture()) { ShortcutKeys = Keys.Control | Keys.N },
                new ToolStripMenuItem("&Nouveau Devis", null, (s, e) => OuvrirNouveauDevis()) { ShortcutKeys = Keys.Control | Keys.D },
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Paramètres Entreprise", null, (s, e) => OuvrirParametresEntreprise()),
                new ToolStripMenuItem("&Utilisateurs", null, (s, e) => OuvrirGestionUtilisateurs()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Sauvegarder", null, (s, e) => SauvegarderDonnees()) { ShortcutKeys = Keys.Control | Keys.S },
                new ToolStripMenuItem("&Restaurer", null, (s, e) => RestaurerDonnees()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Quitter", null, (s, e) => this.Close()) { ShortcutKeys = Keys.Alt | Keys.F4 }
            });

            // Menu Ventes
            var menuVentes = new ToolStripMenuItem("&Ventes");
            menuVentes.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("&Factures", null, (s, e) => OuvrirListeFactures()),
                new ToolStripMenuItem("&Devis", null, (s, e) => OuvrirListeDevis()),
                new ToolStripMenuItem("&Factures Proforma", null, (s, e) => OuvrirListeProforma()),
                new ToolStripMenuItem("&Avoirs", null, (s, e) => OuvrirListeAvoirs()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Clients", null, (s, e) => OuvrirGestionClients()),
                new ToolStripMenuItem("&Suivi Paiements", null, (s, e) => OuvrirSuiviPaiements())
            });

            // Menu Achats
            var menuAchats = new ToolStripMenuItem("&Achats");
            menuAchats.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("&Factures d'Achat", null, (s, e) => OuvrirFacturesAchat()),
                new ToolStripMenuItem("&Commandes d'Achat", null, (s, e) => OuvrirCommandesAchat()),
                new ToolStripMenuItem("&Retours d'Achat", null, (s, e) => OuvrirRetoursAchat()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Fournisseurs", null, (s, e) => OuvrirGestionFournisseurs()),
                new ToolStripMenuItem("&Suivi Paiements", null, (s, e) => OuvrirSuiviPaiementsFournisseurs())
            });

            // Menu Stock
            var menuStock = new ToolStripMenuItem("&Stock");
            menuStock.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("&Produits", null, (s, e) => OuvrirGestionProduits()),
                new ToolStripMenuItem("&Catégories", null, (s, e) => OuvrirGestionCategories()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Mouvements de Stock", null, (s, e) => OuvrirMouvementsStock()),
                new ToolStripMenuItem("&Inventaire", null, (s, e) => OuvrirInventaire()),
                new ToolStripMenuItem("&Ajustements", null, (s, e) => OuvrirAjustementsStock()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&État du Stock", null, (s, e) => OuvrirEtatStock()),
                new ToolStripMenuItem("&Alertes Stock Minimum", null, (s, e) => OuvrirAlertesStock())
            });

            // Menu Comptabilité
            var menuComptabilite = new ToolStripMenuItem("&Comptabilité");
            menuComptabilite.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("&Comptes", null, (s, e) => OuvrirPlanComptable()),
                new ToolStripMenuItem("&Écritures", null, (s, e) => OuvrirEcrituresComptables()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Recettes", null, (s, e) => OuvrirGestionRecettes()),
                new ToolStripMenuItem("&Dépenses", null, (s, e) => OuvrirGestionDepenses()),
                new ToolStripMenuItem("&Opérations de Caisse", null, (s, e) => OuvrirOperationsCaisse()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Bilan", null, (s, e) => OuvrirBilan()),
                new ToolStripMenuItem("&Compte de Résultat", null, (s, e) => OuvrirCompteResultat())
            });

            // Menu Rapports
            var menuRapports = new ToolStripMenuItem("&Rapports");
            menuRapports.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("&Chiffre d'Affaires", null, (s, e) => OuvrirRapportCA()),
                new ToolStripMenuItem("&Ventes par Produit", null, (s, e) => OuvrirRapportVentesProduit()),
                new ToolStripMenuItem("&Ventes par Client", null, (s, e) => OuvrirRapportVentesClient()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&État des Stocks", null, (s, e) => OuvrirRapportStock()),
                new ToolStripMenuItem("&Mouvements de Stock", null, (s, e) => OuvrirRapportMouvements()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&Clients Débiteurs", null, (s, e) => OuvrirRapportDebiteurs()),
                new ToolStripMenuItem("&Fournisseurs Créditeurs", null, (s, e) => OuvrirRapportCrediteurs())
            });

            // Menu Aide
            var menuAide = new ToolStripMenuItem("&Aide");
            menuAide.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("&Manuel d'Utilisation", null, (s, e) => OuvrirManuel()),
                new ToolStripMenuItem("&Raccourcis Clavier", null, (s, e) => AfficherRaccourcis()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("&À Propos", null, (s, e) => AfficherAPropos())
            });

            menuPrincipal.Items.AddRange(new ToolStripItem[]
            {
                menuFichier, menuVentes, menuAchats, menuStock, menuComptabilite, menuRapports, menuAide
            });

            this.MainMenuStrip = menuPrincipal;
            this.Controls.Add(menuPrincipal);
        }

        private void InitializeStatusBar()
        {
            statusStrip = new StatusStrip
            {
                BackColor = Color.FromArgb(52, 73, 94),
                ForeColor = Color.White
            };

            lblUtilisateur = new ToolStripStatusLabel
            {
                Text = $"Utilisateur: {AuthenticationService.GetCurrentUserFullName()}",
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            lblDate = new ToolStripStatusLabel
            {
                Text = DateTime.Now.ToString("dddd dd MMMM yyyy", new System.Globalization.CultureInfo("fr-FR")),
                TextAlign = ContentAlignment.MiddleCenter
            };

            lblHeure = new ToolStripStatusLabel
            {
                Text = DateTime.Now.ToString("HH:mm:ss"),
                TextAlign = ContentAlignment.MiddleRight
            };

            statusStrip.Items.AddRange(new ToolStripItem[] { lblUtilisateur, lblDate, lblHeure });
            this.Controls.Add(statusStrip);
        }

        private void InitializeTimer()
        {
            timerHeure = new System.Windows.Forms.Timer
            {
                Interval = 1000,
                Enabled = true
            };
            timerHeure.Tick += (s, e) =>
            {
                lblHeure.Text = DateTime.Now.ToString("HH:mm:ss");
                lblDate.Text = DateTime.Now.ToString("dddd dd MMMM yyyy", new System.Globalization.CultureInfo("fr-FR"));
            };
        }

        private Image CreateWelcomeBackground()
        {
            var bitmap = new Bitmap(800, 600);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.Clear(Color.FromArgb(236, 240, 241));
                
                // Titre de bienvenue
                var font = new Font("Segoe UI", 24, FontStyle.Bold);
                var brush = new SolidBrush(Color.FromArgb(52, 73, 94));
                var text = "Bienvenue dans le Système de Gestion";
                var size = g.MeasureString(text, font);
                g.DrawString(text, font, brush, (bitmap.Width - size.Width) / 2, 200);
                
                // Sous-titre
                font = new Font("Segoe UI", 16);
                brush = new SolidBrush(Color.FromArgb(127, 140, 141));
                text = "Ventes et Stocks - Version 1.0";
                size = g.MeasureString(text, font);
                g.DrawString(text, font, brush, (bitmap.Width - size.Width) / 2, 250);
                
                // Instructions
                font = new Font("Segoe UI", 12);
                text = "Utilisez le menu ci-dessus pour naviguer dans l'application";
                size = g.MeasureString(text, font);
                g.DrawString(text, font, brush, (bitmap.Width - size.Width) / 2, 320);
            }
            return bitmap;
        }

        // Méthodes pour ouvrir les différents formulaires
        private void OuvrirNouvelleFacture() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirNouveauDevis() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirParametresEntreprise() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirGestionUtilisateurs() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void SauvegarderDonnees() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void RestaurerDonnees() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirListeFactures() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirListeDevis() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirListeProforma() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirListeAvoirs() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirGestionClients() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirSuiviPaiements() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirFacturesAchat() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirCommandesAchat() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirRetoursAchat() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirGestionFournisseurs() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirSuiviPaiementsFournisseurs() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirGestionProduits() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirGestionCategories() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirMouvementsStock() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirInventaire() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirAjustementsStock() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirEtatStock() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirAlertesStock() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirPlanComptable() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirEcrituresComptables() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirGestionRecettes() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirGestionDepenses() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirOperationsCaisse() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirBilan() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirCompteResultat() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirRapportCA() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirRapportVentesProduit() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirRapportVentesClient() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirRapportStock() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirRapportMouvements() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirRapportDebiteurs() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirRapportCrediteurs() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void OuvrirManuel() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        private void AfficherRaccourcis() => MessageBox.Show("Fonctionnalité en cours de développement", "Information");
        
        private void AfficherAPropos()
        {
            MessageBox.Show(
                "Système de Gestion des Ventes et Stocks\n" +
                "Version 1.0.0\n\n" +
                "Développé par EE Solutions\n" +
                "Copyright © 2024\n\n" +
                "Ce logiciel permet la gestion complète des ventes,\n" +
                "achats, stocks et comptabilité de votre entreprise.",
                "À Propos",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            var result = MessageBox.Show(
                "Êtes-vous sûr de vouloir quitter l'application ?",
                "Confirmation",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
            else
            {
                timerHeure?.Stop();
                timerHeure?.Dispose();
            }

            base.OnFormClosing(e);
        }
    }
}

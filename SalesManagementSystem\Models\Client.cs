using System;
using System.Collections.Generic;

namespace SalesManagementSystem.Models
{
    public class Client : BaseEntity
    {
        public string CodeClient { get; set; } = string.Empty;
        public string RaisonSociale { get; set; } = string.Empty;
        public string? Nom { get; set; }
        public string? Prenom { get; set; }
        public TypeClient TypeClient { get; set; }
        
        // Informations de contact
        public string? Adresse { get; set; }
        public string? Ville { get; set; }
        public string? CodePostal { get; set; }
        public string? Pays { get; set; } = "France";
        public string? Telephone { get; set; }
        public string? Mobile { get; set; }
        public string? Fax { get; set; }
        public string? Email { get; set; }
        public string? SiteWeb { get; set; }
        
        // Informations fiscales
        public string? NumeroTVA { get; set; }
        public string? RegistreCommerce { get; set; }
        public string? CodeNAF { get; set; }
        
        // Informations commerciales
        public decimal SoldeInitial { get; set; }
        public decimal SoldeActuel { get; set; }
        public decimal LimiteCredit { get; set; }
        public int DelaiPaiement { get; set; } = 30; // en jours
        public decimal RemiseHabituelle { get; set; }
        public TypePrix TypePrixPreferentiel { get; set; } = TypePrix.PrixVente;
        
        // Statut
        public bool EstActif { get; set; } = true;
        public bool EstBloque { get; set; } = false;
        public string? RaisonBlocage { get; set; }
        public DateTime? DateDernierAchat { get; set; }
        
        // Remarques
        public string? Remarques { get; set; }
        
        // Navigation properties
        public List<Facture> Factures { get; set; } = new List<Facture>();
        public List<Devis> Devis { get; set; } = new List<Devis>();
    }

    public enum TypeClient
    {
        Particulier = 1,
        Entreprise = 2,
        Administration = 3,
        Association = 4
    }

    public enum TypePrix
    {
        PrixVente = 1,
        PrixGros = 2,
        PrixDemiGros = 3
    }
}

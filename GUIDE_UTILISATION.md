# Guide d'Utilisation Rapide
## Système de Gestion des Ventes et Stocks

### 🚀 Démarrage Rapide

#### 1. Premier Lancement
- Lancez l'application `SalesManagementSystem.exe`
- Connectez-vous avec les identifiants par défaut :
  - **Utilisateur** : `admin`
  - **Mot de passe** : `admin123`

#### 2. Configuration Initiale
1. **Paramètres Entreprise** (Menu Fichier > Paramètres Entreprise)
   - Renseignez les informations de votre entreprise
   - Configurez la numérotation des documents
   - Définissez les taux de TVA et timbre

2. **Création des Catégories** (Menu Stock > Catégories)
   - Créez vos catégories principales
   - Ajoutez des sous-catégories si nécessaire

3. **Ajout des Fournisseurs** (Menu Achats > Fournisseurs)
   - Créez vos fournisseurs avec leurs informations complètes
   - Définissez les conditions de paiement

### 📦 Gestion des Produits

#### Ajouter un Produit
1. Menu **Stock > Produits**
2. C<PERSON>z sur **Ajouter**
3. Remplissez les informations :
   - Code produit (unique)
   - Désignation
   - Catégorie et fournisseur
   - Prix d'achat et de vente
   - Stock initial
   - Unités de mesure

#### Prix Multiples
Chaque produit peut avoir plusieurs prix :
- **Prix d'Achat** : Coût d'acquisition
- **Prix de Vente** : Prix de détail
- **Prix de Gros** : Pour les grossistes
- **Prix Demi-Gros** : Prix intermédiaire
- **Prix HT** : Prix hors taxes
- **PMP** : Prix Moyen Pondéré (calculé automatiquement)

#### Codes-Barres
- Ajoutez plusieurs codes-barres par produit
- Définissez un code principal
- Supporté : EAN13, Code128, etc.

### 👥 Gestion des Clients

#### Ajouter un Client
1. Menu **Ventes > Clients**
2. Cliquez sur **Ajouter**
3. Remplissez :
   - Code client (unique)
   - Raison sociale ou nom/prénom
   - Type de client (Particulier, Entreprise, etc.)
   - Adresse complète
   - Informations de contact
   - Limite de crédit
   - Délai de paiement

### 🧾 Création de Factures

#### Nouvelle Facture de Vente
1. Menu **Ventes > Factures** ou `Ctrl+N`
2. Sélectionnez le client
3. Ajoutez les produits :
   - Scannez le code-barres ou saisissez le code
   - Définissez la quantité
   - Le prix se remplit automatiquement
   - Appliquez des remises si nécessaire
4. Vérifiez les totaux (HT, TVA, TTC)
5. Ajoutez le timbre si applicable
6. Enregistrez et imprimez

#### Devis
1. Menu **Ventes > Devis**
2. Même processus qu'une facture
3. Définissez la date de validité
4. Convertissez en facture une fois accepté

### 📊 Gestion du Stock

#### Mouvements de Stock
Le stock est mis à jour automatiquement lors :
- Des ventes (sortie)
- Des achats (entrée)
- Des ajustements manuels
- Des inventaires

#### Inventaire
1. Menu **Stock > Inventaire**
2. Créez un nouvel inventaire
3. Saisissez les quantités physiques
4. Le système calcule les écarts
5. Validez pour ajuster le stock

#### Alertes Stock
- Configurez les seuils minimum par produit
- Recevez des alertes automatiques
- Consultez la liste des produits en rupture

### 💰 Comptabilité

#### Écritures Automatiques
Le système génère automatiquement :
- Écritures de vente
- Écritures d'achat
- Mouvements de caisse
- Calculs de TVA

#### Rapports Financiers
- **Bilan** : Situation financière à une date
- **Compte de Résultat** : Résultats sur une période
- **Chiffre d'Affaires** : Évolution des ventes
- **Débiteurs/Créditeurs** : Suivi des impayés

### 📈 Rapports et Analyses

#### Rapports Disponibles
1. **Ventes**
   - Chiffre d'affaires par période
   - Ventes par produit
   - Ventes par client
   - Évolution des ventes

2. **Stock**
   - État du stock
   - Mouvements de stock
   - Valorisation du stock
   - Produits à réapprovisionner

3. **Financiers**
   - Clients débiteurs
   - Fournisseurs créditeurs
   - Échéancier des paiements

#### Export des Données
- **PDF** : Pour impression et archivage
- **Excel** : Pour analyse approfondie
- **Sauvegarde** : Backup complet des données

### 🔧 Administration

#### Gestion des Utilisateurs
1. Menu **Fichier > Utilisateurs**
2. Créez des comptes utilisateurs
3. Définissez les permissions :
   - Voir, Ajouter, Modifier, Supprimer
   - Imprimer, Exporter
   - Accès administrateur

#### Types d'Utilisateurs
- **Administrateur** : Accès complet
- **Gestionnaire** : Gestion commerciale
- **Vendeur** : Ventes uniquement
- **Magasinier** : Stock uniquement
- **Comptable** : Comptabilité et rapports
- **Consultation** : Lecture seule

#### Sauvegarde
- **Automatique** : Programmable (quotidienne, hebdomadaire)
- **Manuelle** : Menu Fichier > Sauvegarder
- **Restauration** : Menu Fichier > Restaurer

### ⌨️ Raccourcis Clavier

| Raccourci | Action |
|-----------|--------|
| `Ctrl+N` | Nouvelle facture |
| `Ctrl+D` | Nouveau devis |
| `Ctrl+S` | Sauvegarder |
| `Ctrl+P` | Imprimer |
| `Ctrl+F` | Rechercher |
| `F1` | Aide |
| `F5` | Actualiser |
| `Échap` | Annuler |

### 🎯 Conseils d'Utilisation

#### Bonnes Pratiques
1. **Sauvegardez régulièrement** vos données
2. **Configurez les alertes** de stock minimum
3. **Utilisez les codes-barres** pour accélérer la saisie
4. **Créez des packs** pour les produits vendus ensemble
5. **Définissez des promotions** pour les périodes spéciales

#### Optimisation
- Utilisez les **raccourcis clavier** pour gagner du temps
- Configurez les **prix par défaut** selon vos marges
- Activez la **numérotation automatique** des documents
- Utilisez les **filtres** dans les listes pour trouver rapidement

### 🆘 Résolution de Problèmes

#### Problèmes Courants
1. **Connexion impossible**
   - Vérifiez les identifiants
   - Contactez l'administrateur

2. **Stock négatif**
   - Vérifiez les paramètres de stock
   - Effectuez un inventaire

3. **Erreur d'impression**
   - Vérifiez l'imprimante par défaut
   - Consultez l'aperçu avant impression

4. **Lenteur du système**
   - Fermez les applications inutiles
   - Effectuez une sauvegarde/nettoyage

#### Support
- Consultez l'aide intégrée (`F1`)
- Vérifiez les logs d'erreur
- Contactez le support technique

### 📞 Contact et Support

Pour toute assistance :
- **Documentation** : Menu Aide > Manuel d'utilisation
- **Support technique** : Contactez EE Solutions
- **Mises à jour** : Vérification automatique disponible

---

**Développé par EE Solutions - Version 1.0.0**
